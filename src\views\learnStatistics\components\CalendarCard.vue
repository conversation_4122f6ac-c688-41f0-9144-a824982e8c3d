<template>
  <!-- 
    学习进程日历卡片组件
    实现了学习进程的展示 关键代码有详细注释
   -->
  <div class="calendar">
    <div class="cal-header">
      <div class="title">每天学习时长统计（分钟）</div>
      <div class="right-month">
        <div class="go-today" @click="goToday">今天</div>
      </div>
    </div>
    <div class="cal-body">
      <div v-if="totalContent == 0">
        <div class="none-content">
          <el-empty description="暂无学习记录"></el-empty>
        </div>
      </div>
      <table v-else>
        <thead>
          <tr>
            <th class="up-name" style="border-width: 2px 0 0 2px">
              <div class="month">
                <img style="cursor: pointer; margin-right: 10px" src="@/assets/images/learn/doublerightrow.svg" alt="月份向左"
                  @click="backMonth" />
                <img style="cursor: pointer; transform: rotate(180deg); margin-right: 10px" src="@/assets/images/learn/rightrow.svg" alt="向左"
                  @click="backDay" />
                <span>{{ showDate }}</span>
                <img style="cursor: pointer; margin-left: 10px"
                  src="@/assets/images/learn/rightrow.svg" alt="向右" @click="nextDay" />
                <img style="cursor: pointer; transform: rotate(180deg); margin-left: 10px"
                  src="@/assets/images/learn/doublerightrow.svg" alt="月份向右" @click="nextMonth" />
              </div>
            </th>
            <th v-for="date in dateArray" :key="date.day">{{ date.day }}</th>
          </tr>
        </thead>
        <tbody>
          <!-- 有多少条数据就有多少行 -->

          <!-- 新的注释 -->
          <!-- <tr v-if="totalContent == 0">
            <td colspan="22">
              <div class="none-content">
                <el-empty description="暂无学习记录"></el-empty>
              </div>
            </td>
          </tr> -->


          <!--<tr v-for="item in learnInfo" :key="item.title" v-else>
            <td class="first-td" @click="goPage(item)">{{ item.title }}</td>
            <td v-for="date in dateArray" :key="date.key">
              <div v-if="
                // @ts-ignore
                item.timeList[0][date.key]
                " style="
                  width: 34px;
                  height: 20px;
                  border-radius: 4px;
                  background-color: var(--color-theme-project);
                  color: white;
                  font-size: var(--fontsize-small-project);
                  margin: 0 auto;
                  line-height: 20px;
                ">
                {{
                  //@ts-ignore
                  item.timeList[0][date.key]
                }}
              </div>
              <div class="continueLearn" v-if="isToday(date.year, date.month, date.day)">
                <img style="cursor: pointer" src="@/assets/images/learn/continuelearning.svg" @click="goPage(item)" />
                <div style="
                  width: 34px;
                  height: 20px;
                  border-radius: 4px;
                  border: 1px solid var(--color-theme-project);
                  background-color: white;
                  color: var(--color-theme-project);
                  font-size: var(--fontsize-small-project);
                  margin: 0 auto;
                  line-height: 20px;">
                  {{
                    //@ts-ignore
                    0
                  }}
                </div>
              </div>
            </td>
          </tr>-->
          <tr v-if="totalContent != 0" v-for="rowIndex in 5" :key="rowIndex">
            <!-- 当有数据时显示内容，无数据时显示空行 -->
            <td class="first-td" @click="rowIndex <= learnInfo.length && goPage(learnInfo[rowIndex - 1])">
              <div v-if="rowIndex <= learnInfo.length">{{ learnInfo[rowIndex - 1].title }}</div>
              <div v-else></div>
            </td>

            <!-- 日期单元格 -->
            <td v-for="date in dateArray" :key="date.key">
              <!-- 有数据且有时间记录 -->
              <div
                v-if="rowIndex <= learnInfo.length && learnInfo[rowIndex - 1].timeList && learnInfo[rowIndex - 1].timeList[0][date.key]"
                style="width: 24px; height: 24px; border-radius: 50%; background-color: var(--color-theme-project); color: white; font-size: var(--fontsize-small-project); margin: 0 auto; line-height: 24px;">
                {{ learnInfo[rowIndex - 1].timeList[0][date.key] }}
              </div>

              <!-- 今天的日期显示继续学习按钮 -->
              <div class="continueLearn" v-else-if="rowIndex <= learnInfo.length && isToday(date.year, date.month, date.day)">
                <div
                  style="width: 24px; height: 24px; border-radius: 50%; border: 1px solid var(--color-theme-project); background-color: white; color: var(--color-theme-project); font-size: var(--fontsize-small-project); margin: 0 auto; line-height: 24px;">
                  0
                </div>
              </div>

              <!-- 空状态 -->
              <div v-else style="width: 34px; height: 20px;"></div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="pagination">
      <MyPagination :total="calTotalPages" :is-hori="false" v-model="calCurrentPage" @pageChange="handlerCalPageChange">
      </MyPagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, computed } from 'vue';
import MyPagination from './MyPagination.vue';
//@ts-ignore  隐藏报错实际上是可以用的
import { DateTime } from 'luxon';
import { ElMessage } from 'element-plus';
// 引入api函数
import { getLearnProcessApi, getLearnListApi } from '@/apis/learnStatistics';
import { PrjForm, PrjType } from '@/types/project';
import { GoodsType } from '@/types/goods';
import { useLearnStore } from '@/stores/learnintro';
const learnStore = useLearnStore();
const dt = DateTime.local();
const dateArray: Array<MyDate> = reactive([]);
const router = useRouter();
// 定义一个日期类型
type MyDate = {
  year: number;
  month: number;
  day: number;
  key: string;
};

const learning = defineProps(['learned']);
watch(learning, (newVal, oldVal) => {
  console.log('learned prop has changed:', newVal);
  getCalendarInfo();
  // 在 learned 变化时执行其他逻辑
  // your additional logic here
});
// 窗口控制 窗口大小设置为30
// 上一天触发函数
const backDay = () => {
  // @ts-ignore
  calEndDate.value.label = calEndDate.value.value.plus({ day: -1 }).toISODate();
  //@ts-ignore
  calEndDate.value.value = calEndDate.value.value.plus({ day: -1 });
  // @ts-ignore
  calStartDate.value.label = calStartDate.value.value.plus({ day: -1 }).toISODate();
  // @ts-ignore
  calStartDate.value.value = calStartDate.value.value.plus({ day: -1 });
  getCalendarInfo();

  // datearray数组删除最后一个，而且在第一个位置插入一个日期，为当前datearray[0]的日期减一天，考虑跨年跨月哦
  // 如果当前日期为1号，则需要将日期设置为上个月的最后一天
  const currentMonth = dateArray[0].month;
  const currentYear = dateArray[0].year;
  const currentDay = dateArray[0].day;
  const currentDate = DateTime.fromObject({ month: currentMonth, year: currentYear, day: currentDay });
  const lastDay = currentDate.minus({ day: 1 });
  dateArray.pop();
  dateArray.unshift({
    year: lastDay.year,
    month: lastDay.month,
    day: lastDay.day,
    key: lastDay.month + '-' + lastDay.day
  });
};
// 上一个月触发函数
const backMonth = () => {
  if (dateArray[0].day == 1) {
    //@ts-ignore
    // 开始时间为上月16号
    const current = calStartDate.value.value;
    const start = current.set({ day: 16 });
    const lastMonth = start.minus({ month: 1 });
    calStartDate.value.label = lastMonth.toISODate();
    calStartDate.value.value = lastMonth;
    // 每次拿16天的数据
    calEndDate.value.label = calStartDate.value.value.plus({ day: 15 }).toISODate();
    calEndDate.value.value = calStartDate.value.value.plus({ day: 15 });
  } else if (dateArray[0].day == 16) {
    //@ts-ignore
    // 开始时间为当月1号
    const current = calStartDate.value.value;
    const start = current.set({ day: 1 });
    calStartDate.value.label = start.toISODate();
    calStartDate.value.value = start;
    // 每次拿16天的数据
    calEndDate.value.label = calStartDate.value.value.plus({ day: 15 }).toISODate();
    calEndDate.value.value = calStartDate.value.value.plus({ day: 15 });
  } else if (dateArray[0].day > 16) {
    //@ts-ignore
    // 开始时间为当前月16号
    const current = calStartDate.value.value;
    const start = current.set({ day: 16 });
    calStartDate.value.label = start.toISODate();
    calStartDate.value.value = start;
    // 每次拿16天的数据
    calEndDate.value.label = calStartDate.value.value.plus({ day: 15 }).toISODate();
    calEndDate.value.value = calStartDate.value.value.plus({ day: 15 });
  } else if (dateArray[0].day < 16) {
    //@ts-ignore
    // 开始时间为当前月1号
    const current = calStartDate.value.value;
    const start = current.set({ day: 1 });
    calStartDate.value.label = start.toISODate();
    calStartDate.value.value = start;
    // 每次拿16天的数据
    calEndDate.value.label = calStartDate.value.value.plus({ day: 15 }).toISODate();
    calEndDate.value.value = calStartDate.value.value.plus({ day: 15 });
  }
  getCalendarInfo();
  //判断跳转逻辑
  if (dateArray[0].day == 1) {
    //拿出dateArray[0]的月份
    const currentMonth = dateArray[0].month;
    const currentYear = dateArray[0].year;
    dateArray.length = 0;
    //开始时间为上月16号，依次放16天,考虑跨年哦
    let lastMonth = DateTime.fromObject({ month: currentMonth - 1, year: currentYear, day: 16 });
    if (currentMonth == 1) {
      lastMonth = DateTime.fromObject({ month: 12, year: currentYear - 1, day: 16 });
    } else {
      lastMonth = DateTime.fromObject({ month: currentMonth - 1, year: currentYear, day: 16 });
    }
    for (let i = 0; i < 16; i++) {
      const lastDay = lastMonth.plus({ day: 1 * i });
      dateArray.push({
        year: lastDay.year,
        month: lastDay.month,
        day: lastDay.day,
        key: lastDay.month + '-' + lastDay.day
      });
    }
  } else if (dateArray[0].day < 16) {
    //开始时间为当月1号，按顺序依次放16天
    let currentMonth = dateArray[0].month;
    let currentYear = dateArray[0].year;
    dateArray.length = 0;
      let current = DateTime.fromObject({ month: currentMonth, year: currentYear, day: 1 });
      for (let i = 0; i < 16; i++) {
        const lastDay = current.plus({ day: 1 * i });
        dateArray.push({
          year: lastDay.year,
          month: lastDay.month,
          day: lastDay.day,
          key: lastDay.month + '-' + lastDay.day
        });
      }
  } else if (dateArray[0].day == 16) {
    //开始时间为当月1号，按顺序依次放16天
    let currentMonth = dateArray[0].month;
    let currentYear = dateArray[0].year;
    dateArray.length = 0;
    let current = DateTime.fromObject({ month: currentMonth, year: currentYear, day: 1 });
    for (let i = 0; i < 16; i++) {
      const lastDay = current.plus({ day: 1 * i });
      dateArray.push({
        year: lastDay.year,
        month: lastDay.month,
        day: lastDay.day,
        key: lastDay.month + '-' + lastDay.day
      });
    }
  } else if (dateArray[0].day > 16) {
    //开始时间为当月16号，按顺序依次放16天
    let currentMonth = dateArray[0].month;
    let currentYear = dateArray[0].year;
    dateArray.length = 0;
    let current = DateTime.fromObject({ month: currentMonth, year: currentYear, day: 16 });
    for (let i = 0; i < 16; i++) {
      const lastDay = current.plus({ day: 1 * i });
      dateArray.push({
        year: lastDay.year,
        month: lastDay.month,
        day: lastDay.day,
        key: lastDay.month + '-' + lastDay.day
      });
    }
  }
};
// 下一天触发函数
const nextDay = () => {
  //如果dateArray[]里面有今天，不允许执行下面的函数，提示"已经到底了"
  if (dateArray.some(item => item.year == dt.year && item.month == dt.month && item.day == dt.day)) {
    ElMessage.warning('没有更多数据了');
    return;
  }
  
  // 判断最后一天
  // 判断当前最后一天是否与结束日期相等
  // @ts-ignore
  calStartDate.value.label = calStartDate.value.value.plus({ day: 1 }).toISODate();
  //@ts-ignore
  calStartDate.value.value = calStartDate.value.value.plus({ day: 1 });
  // @ts-ignore
  calEndDate.value.label = calEndDate.value.value.plus({ day: 1 }).toISODate();
  // @ts-ignore
  calEndDate.value.value = calEndDate.value.value.plus({ day: 1 });
  getCalendarInfo();
  // 偏移量加1
  const currentMonth = dateArray[15].month;
  const currentYear = dateArray[15].year;
  const currentDay = dateArray[15].day;
  const currentDate = DateTime.fromObject({ month: currentMonth, year: currentYear, day: currentDay });
  const nextDay = currentDate.plus({ day: 1 });
  // 删除第一个元素,在最后一个位置插入一个元素,为当前datearray[15]的日期加一天
  //删除第一个元素
  dateArray.shift();
  //在最后一个位置插入一个元素,为当前datearray[15]的日期加一天
  dateArray.push({
    year: nextDay.year,
    month: nextDay.month,
    day: nextDay.day,
    key: nextDay.month + '-' + nextDay.day
  });

};
//下一个月触发函数
const nextMonth = () => {
  //如果dateArray[]里面有今天，不允许执行下面的函数，提示"已经到底了"
  if (dateArray.some(item => item.year == dt.year && item.month == dt.month && item.day == dt.day)) {
    ElMessage.warning('没有更多数据了');
    return;
  }

  if (dateArray[0].day == 1) {
    //@ts-ignore
    // 开始时间为当月16号
    const current = calStartDate.value.value;
    const start = current.set({ day: 16 });
    calStartDate.value.label = start.toISODate();
    calStartDate.value.value = start;
    // 每次拿16天的数据
    calEndDate.value.label = calStartDate.value.value.plus({ day: 15 }).toISODate();
    calEndDate.value.value = calStartDate.value.value.plus({ day: 15 });
  } else if (dateArray[0].day == 16) {
    //@ts-ignore
    // 开始时间为下月1号
    const current = calStartDate.value.value;
    const start = current.set({ day: 1 });
    const nextMonth = start.plus({ month: 1 });
    calStartDate.value.label = nextMonth.toISODate();
    calStartDate.value.value = nextMonth;
    // 每次拿16天的数据
    calEndDate.value.label = calStartDate.value.value.plus({ day: 15 }).toISODate();
    calEndDate.value.value = calStartDate.value.value.plus({ day: 15 });
  } else if (dateArray[0].day > 16) {
    //@ts-ignore
    // 开始时间为下月1号
    const current = calStartDate.value.value;
    const start = current.set({ day: 1 });
    const nextMonth = start.plus({ month: 1 });
    calStartDate.value.label = nextMonth.toISODate();
    calStartDate.value.value = nextMonth;
    // 每次拿16天的数据
    calEndDate.value.label = calStartDate.value.value.plus({ day: 15 }).toISODate();
    calEndDate.value.value = calStartDate.value.value.plus({ day: 15 });
  } else if (dateArray[0].day < 16) {
    //@ts-ignore
    // 开始时间为当月16号
    const current = calStartDate.value.value;
    const start = current.set({ day: 16 });
    calStartDate.value.label = start.toISODate();
    calStartDate.value.value = start;
    // 每次拿16天的数据
    calEndDate.value.label = calStartDate.value.value.plus({ day: 15 }).toISODate();
    calEndDate.value.value = calStartDate.value.value.plus({ day: 15 });
  }
  getCalendarInfo();
  //判断跳转逻辑
  if (dateArray[0].day == 1) {
    //拿出dateArray[0]的月份
    const currentMonth = dateArray[0].month;
    const currentYear = dateArray[0].year;
    dateArray.length = 0;
    //开始时间为这个月16号，依次放16天,考虑跨年哦
    let current = DateTime.fromObject({ month: currentMonth, year: currentYear, day: 16 });
    for (let i = 0; i < 16; i++) {
      const lastDay = current.plus({ day: 1 * i });
      dateArray.push({
        year: lastDay.year,
        month: lastDay.month,
        day: lastDay.day,
        key: lastDay.month + '-' + lastDay.day
      });
    }
  } else if (dateArray[0].day < 16) {
    //开始时间为当月16号，按顺序依次放16天
    let currentMonth = dateArray[0].month;
    let currentYear = dateArray[0].year;
    dateArray.length = 0;
      let current = DateTime.fromObject({ month: currentMonth, year: currentYear, day: 16 });
      for (let i = 0; i < 16; i++) {
        const lastDay = current.plus({ day: 1 * i });
        dateArray.push({
          year: lastDay.year,
          month: lastDay.month,
          day: lastDay.day,
          key: lastDay.month + '-' + lastDay.day
        });
      }
  } else if (dateArray[0].day == 16) {
    //开始时间为下月1号，按顺序依次放16天
    let currentMonth = dateArray[0].month;
    let currentYear = dateArray[0].year;
    dateArray.length = 0;
    //考虑跨年哦
    let current = DateTime.fromObject({ month: currentMonth + 1, year: currentYear, day: 1 });
    if (currentMonth == 12) {
      current = DateTime.fromObject({ month: 1, year: currentYear + 1, day: 1 });
    } else {
      current = DateTime.fromObject({ month: currentMonth + 1, year: currentYear, day: 1 });
    }
    for (let i = 0; i < 16; i++) {
      const lastDay = current.plus({ day: 1 * i });
      dateArray.push({
        year: lastDay.year,
        month: lastDay.month,
        day: lastDay.day,
        key: lastDay.month + '-' + lastDay.day
      });
    }
  } else if (dateArray[0].day > 16) {
    //开始时间为下月1号，按顺序依次放16天
    let currentMonth = dateArray[0].month;
    let currentYear = dateArray[0].year;
    dateArray.length = 0;
    let current = DateTime.fromObject({ month: currentMonth + 1, year: currentYear, day: 1 });
    if (currentMonth == 12) {
      current = DateTime.fromObject({ month: 1, year: currentYear + 1, day: 1 });
    } else {
      current = DateTime.fromObject({ month: currentMonth + 1, year: currentYear, day: 1 });
    }
    for (let i = 0; i < 16; i++) {
      const lastDay = current.plus({ day: 1 * i });
      dateArray.push({
        year: lastDay.year,
        month: lastDay.month,
        day: lastDay.day,
        key: lastDay.month + '-' + lastDay.day
      });
    }
  }

};
// 本月日期数组生成
const getDays = () => {
  // 将当天放入
  dateArray.push({
    year: dt.year,
    month: dt.month,
    day: dt.day,
    key: dt.month + '-' + dt.day
  });

  //计算当前所在月
  if (dt.day <= 15) {
    //日期小于等于15，均为前半月，都放入15天
    //固定16天
    //往前放到1
    const frontDay = dt.day - 1;
    for (let i = 1; i <= frontDay; i++) {
      const lastDay = dt.plus({ day: -1 * i });
      dateArray.unshift({
        year: lastDay.year,
        month: lastDay.month,
        day: lastDay.day,
        key: lastDay.month + '-' + lastDay.day
      });
    }
    //往后放到16
    const afterDay = 16 - dt.day;
    for (let i = 1; i <= afterDay; i++) {
      const lastDay = dt.plus({ day: 1 * i });
      dateArray.push({
        year: lastDay.year,
        month: lastDay.month,
        day: lastDay.day,
        key: lastDay.month + '-' + lastDay.day
      });
    }
  } else {
    const frontDay = dt.day - 16;
    for (let i = 1; i <= frontDay; i++) {
      const lastDay = dt.plus({ day: -1 * i });
      dateArray.unshift({
        year: lastDay.year,
        month: lastDay.month,
        day: lastDay.day,
        key: lastDay.month + '-' + lastDay.day
      });
    }
    //后半月
    //统一16天啊
    const afterDay = 31 - dt.day;
    for (let i = 1; i <= afterDay; i++) {
      const lastDay = dt.plus({ day: 1 * i });
      dateArray.push({
        year: lastDay.year,
        month: lastDay.month,
        day: lastDay.day,
        key: lastDay.month + '-' + lastDay.day
      });
    }
  }
};

// 回到今天函数
const goToday = () => {

  dateArray.length = 0;
  // 恢复状态后 重新获取
  getDays();
  // 恢复参数重新请求
  initDateParams();
  getCalendarInfo();
};

// 是否是今天
const isToday = (year: number, month: number, day: number) => {
  if (year == dt.year && month == dt.month && day == dt.day) {
    return true;
  }
  return false;
};

// 控制展示内容变量
const isCrossYear = ref(false);
// 计算展示内容
const showDate = computed(() => {
  if (dateArray.length == 0) {
    return '';
  }
  let resultStr = '';
  // 根据数组元素进行计算
  const firstDay = dateArray[0];
  const lastDay = dateArray[dateArray.length - 1];
  console.log(firstDay, lastDay);

  // 先判断是不是一年 在判断是不是一个月
  if (firstDay.year == lastDay.year) {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    isCrossYear.value = false;
    //如果是同一年  判断一下是否是通过一个月
    if (firstDay.month == lastDay.month) {
      resultStr = `${firstDay.year}年${firstDay.month}月`;
    } else {
      resultStr = `${firstDay.year}年${firstDay.month}-${lastDay.month}月`;
    }
  } else {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    isCrossYear.value = true;
    resultStr = `${firstDay.year}年${firstDay.month}月-${lastDay.year}年${lastDay.month}月`;
  }
  return resultStr;
});

// 挂在时初始化日期数据
onMounted(getDays);

// 日历控件部分 分页控制
let calTotalPages = ref(5);
let calCurrentPage = ref(1);
const handlerCalPageChange = (newPages: number) => {
  calCurrentPage.value = newPages;
  getCalendarInfo();
};

// 展示数据结构
const learnInfo = ref([
  {
    coverPic: '',
    graspKlg: 0,
    klgNumbers: 0,
    // lastLearnedTime: string;
    learned: 0,
    processEndTime: '',
    // processSectionName: string;
    startTime: '',
    title: '',
    userCoverPic: '',
    userName: '',
    // 传递到下一页的参数
    goodsType: GoodsType.common,
    //跳转下一页需要用到的参数!!
    //其中
    //    测评跳转至介绍页不跳转至详情页，也需要用learntype进行判断
    prjType: PrjType.case,
    goodId: 0,
    uniqueCode: '',
    currentLearning: {
      uniqueCode: '', //标志小结的Id
      chapterId: 0,
      prjForm: PrjForm.video,
      endTimeTag: '',
      sectionTitle: ''
    }
  }
]);
const totalContent = ref(0);
const calStartDate = ref({
  label: '',
  value: null
});
const calEndDate = ref({
  label: '',
  value: null
});

const initDateParams = () => {

  // 如果dateArray[0]的日期为16号，则开始日期为这个月16号，一共获取16天
  // 如果dateArray[0]的日期为1号，则开始日期为这个月1号，一共获取16天
  // 如果dateArray[0]的日期为2-15号，则开始日期为这个月1号，一共获取16天
  // 如果dateArray[0]的日期为17-31号，则开始日期为这个月16号，一共获取16天
  //按照上面的写
  if (dt.day == 1) {
    const start = dt.set({ day: 1 });
    calStartDate.value.label = start.toISODate()!;
    calStartDate.value.value = start;
    calEndDate.value.label = start.plus({ day: 15 }).toISODate()!;
    calEndDate.value.value = start.plus({ day: 15 });
  } else if (dt.day == 16) {
    const start = dt.set({ day: 16 });
    calStartDate.value.label = start.toISODate()!;
    calStartDate.value.value = start;
    calEndDate.value.label = start.plus({ day: 15 }).toISODate()!;
    calEndDate.value.value = start.plus({ day: 15 });
  } else if (dt.day > 1 && dt.day < 16) {
    //开始日期为这个月1号，一共获取15天
    //这个月1号!!!
    const start = dt.set({ day: 1 });
    calStartDate.value.label = start.toISODate()!;
    calStartDate.value.value = start;
    calEndDate.value.label = start.plus({ day: 15 }).toISODate()!; 
    calEndDate.value.value = start.plus({ day: 15 });

  } else if (dt.day > 16 && dt.day <= 31) {
    //开始日期为这个月16号，一共获取16天
    const start = dt.set({ day: 16 });
    calStartDate.value.label = start.toISODate()!;
    calStartDate.value.value = start;
    calEndDate.value.label = start.plus({ day: 15 }).toISODate()!;
    calEndDate.value.value = start.plus({ day: 15 });

  }
};
const getCalendarInfo = () => {
  const params = {
    current: calCurrentPage.value,
    limit: 5,
    startTime: calStartDate.value.label,
    endTime: calEndDate.value.label,
    learned: learning.learned
    // current: 1,
    // limit: 5,
    // startTime: "2023-08-01",
    // endTime: "2024-01-12"
  };
  getLearnListApi(params)
    .then((res) => {
      console.log(res);
      // 清空原数据
      learnInfo.value.length = 0;
      // 处理数据
      // @ts-ignore
      learnInfo.value = res.data.list;
      calTotalPages.value = res.data.totalPage;
      totalContent.value = res.data.total;
    })
    .catch((error) => {
      console.log(error);
    });
};
initDateParams();
getCalendarInfo();
// 学习页正在学卡片目前只有案例与领域项目，所以只需要对这两种项目的跳转进行判断，后续可根据需求优化
const goPage = async (learnitem: any) => {
  //在此之前先要判断跳转介绍页还是详情页
  try {
    let path: string;
    if (learnitem.goodsType == GoodsType.vip) {
      // 领域跳转介绍页
      path = '/case';
    }
    //案例跳转详情页
    //改成介绍页
    else {
      //path = '/learning';
      path = '/goodIntroduce'
    }
    const { href } = router.resolve({
      path: path,
      query: {
        spuId: learnitem.spuId
      }
    });
    window.open(href, '_blank');
  } catch (error) {
    console.error('Error setting intro data:', error);
  }
};
</script>

<style lang="less" scoped>
.calendar {
  width: 100%;
  //height: 346px;
  //background-color: var(--vt-c-white-mute);
  border-radius: 5px;
  //margin-top: 23px;
  //padding: 10px 10px 20px;

  .cal-header {
    background-color: white;
    padding-top: 10px;
    width: 99%;
    height: 30px;
    display: flex;
    align-items: center;

    .title {
      width: 210px;
      height: 100%;
      padding-left: 30px;
      //color: var(--color-theme-project);
      //color: #333333;
      font-family: '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
      font-weight: 400;
      font-style: normal;
      font-size: 14px;
      color: #1973CB;
    }

    .right-month {
      width: calc(100% - 210px);
      height: 20px;
      display: flex;
      align-items: center;
      //justify-content: space-between;
      justify-content: flex-end;

      //.month {
      //height: 100%;
      //display: flex;
      //justify-content: space-between;
      //align-items: center;


      //:is(span) {
      //color: var(--color-theme-project);
      //}
      //}

      .go-today {
        margin: 0 20px;
        height: 100%;
        width: 80px;
        background-color: var(--color-theme-project);
        border-radius: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: var(--fontsize-small-project);
        color: white;
        cursor: pointer;
      }

      .go-today:hover {
        color: var(--color-theme-project);
        background-color: #d3dee3;
        border-width: 1px;
        border-color: var(--color-theme-project);
        border-style: solid;
      }
    }

    .right-min {
      font-size: var(--fontsize-small-project);
      color: var(--color-theme-project);
    }
  }

  .cal-body {
    width: 99%;
    display: flex;
    background-color: white;
    justify-content: center;
    padding-left: 10px;

    .left-name {
      width: 132px;
    }

    table {
      margin-top: 10px;
    }

    th,
    td {
      //width: 43px;
      width: 56px;
      height: 34px;
      background-color: white;
      //color: var(--color-theme-project);
      //color: #333333;
      color: #0D2040;
      border: 2px solid #f2f2f2;
      font-size: 14px;
    }

    .up-name {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: row;
      height: 34px;
      //width: 210px;
      width: 100%;
      // background-color: var(--vt-c-white-mute);
      background-color: #ffffff;
      padding-left: 5px;
      padding-right: 5px;

      :is(span) {
        font-size: var(--fontsize-middle-project);
        font-weight: 400;
        //color: var(--color-theme-project);
        color: #0D2040;
      }

      .month {
        height: 30px;
        //width: 168px;
        //padding-left: 5px;
        //padding-right: 5px;
        margin: auto;
        padding-top: 6px;
      }
    }

    .none-content {
      display: flex;
      height: 250px;
      justify-content: center;
      align-items: center;
      width: 100%;
    }

    .first-td {
      height: 30px;
      //width: 168px;
      width: 240px;
      padding-left: 10px;
      padding-right: 5px;
      text-align: left;
    }

    .first-td:hover {
      cursor: pointer;
    }

    td {
      text-align: center;
      vertical-align: middle;
      height: 45px;
    }
  }

  .continueLearn {
    //cursor: pointer;
    white-space: pre-wrap;
  }

  .pagination {
    height: 30px;
    width: 99%;
    background-color: #ffffff;
  }
}
</style>
