import { nextTick, type Ref } from 'vue';
import { Render } from '@endlessorigin/select_to_ask';

/**
 * Render实例管理配置接口
 */
export interface RenderManagerConfig {
  /** DOM容器选择器 */
  containerSelector: string;
  /** 获取内容数据的函数 */
  getContentData: () => any;
  /** 问题列表 */
  questionList: Ref<any>;
  /** select事件处理器 */
  onSelect?: (data: Event) => void;
  /** click事件处理器 */
  onClick?: (data: Event) => void;
  onFinish?: (arg: any) => void;
  /** 是否启用调试日志 */
  enableDebugLog?: boolean;
}

/**
 * 可复用的Render实例管理组合式函数
 * 解决频繁重新初始化导致的事件监听器重复绑定问题
 */
export function useRenderManager(config: RenderManagerConfig) {
  let render: Render | null = null;
  const {
    containerSelector,
    getContentData,
    questionList,
    onSelect,
    onClick,
    onFinish,
    enableDebugLog = true
  } = config;

  /**
   * 调试日志输出
   */
  const debugLog = (message: string, ...args: any[]) => {
    if (enableDebugLog) {
      console.log(`[RenderManager] ${message}`, ...args);
    }
  };

  /**
   * 清理Render实例
   */
  const destroyRenderInstance = () => {
    if (render) {
      render.resetMatchOrSearch(true, true);

      render.destroy();
      debugLog('✅ Render实例已清理');
    }
  };

  /**
   * 绑定Render实例的事件监听器 - 优化版本，避免重复绑定
   */
  const bindEventListeners = () => {
    if (!render) {
      debugLog('⚠️ render实例不存在，跳过事件绑定');
      return;
    }
    // 绑定划词选择事件
    if (onSelect) {
      render.on('select', (data: any) => {
        debugLog('划词选择事件onSelect', data);

        // 创建一个包含Selection对象的新数据结构
        const processedData = {
          ...data,
          selection: window.getSelection() // 添加真正的Selection对象
        };

        onSelect(processedData);
      });
    }

    // 绑定点击事件
    if (onClick) {
      render.on('click', (data: any) => {
        debugLog('点击事件:', data);
        onClick(data);
      });
    }

    // 绑定完成事件
    if (onFinish) {
      render.on('finish', (arg: any) => {
        debugLog('finish事件触发');
        onFinish(arg);
      });
    }

    debugLog('✅ 事件监听器绑定完成');
  };

  /**
   * 处理已有的问题列表
   */
  const processExistingQuestions = () => {
    debugLog('processExistingQuestionsRender:', render);
    debugLog('questionList', questionList.value);
    debugLog(`🔗 处理${questionList.value.length}个已有问题...`);
    questionList.value.forEach((item: any) => {
      if (item.associatedWords) {
        render?.handleQuestion({
          question: item.associatedWords,
          qid: Number(item.questionId)
        });
      }
    });
  };

  /**
   * 等待容器就绪的辅助函数
   */
  const waitForContainer = async (maxRetries = 10, delay = 100): Promise<Element | null> => {
    for (let i = 0; i < maxRetries; i++) {
      const container = document.querySelector(containerSelector);
      if (container) {
        debugLog(`✅ 容器在第${i + 1}次尝试后找到`);
        return container;
      }
      debugLog(`⏳ 第${i + 1}次尝试未找到容器，等待${delay}ms后重试`);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
    return null;
  };

  /**
   * 强制触发渲染完成事件
   */
  const forceFinishEvent = () => {
    if (render && onFinish) {
      debugLog('🔧 强制触发finish事件（questionList为空时的兜底处理）');
      const contentData = getContentData();
      // 直接调用onFinish回调，传入内容数据
      onFinish({ content: contentData });
    }
  };

  /**
   * 初始化Render实例（增强版）
   */
  const initializeRender = async () => {
    // 使用重试机制等待容器就绪
    const container = await waitForContainer();
    const contentData = getContentData();

    // 清理旧实例
    destroyRenderInstance();
    // await nextTick();

    debugLog('🚀 创建新的Render实例');
    debugLog('contentData:', contentData);

    try {
      render = new Render({
        container: containerSelector,
        data: contentData
      });
      bindEventListeners();
      processExistingQuestions();

      // 🔧 修复：当questionList为空时，强制触发finish事件
      if (!questionList.value || questionList.value.length === 0) {
        debugLog('⚠️ questionList为空，将强制触发finish事件');
        // 使用nextTick确保Render实例完全初始化后再触发
        await nextTick();
        forceFinishEvent();
      }
      debugLog('✅ Render实例初始化完成');
    } catch (error: any) {
      debugLog('❌ Render实例初始化失败:', error);
      render = null;

      // 如果是DOM相关错误，尝试延迟重试
      if (error?.message?.includes('insertBefore') || error?.message?.includes('DOM')) {
        debugLog('🔄 检测到DOM错误，将在1秒后重试初始化');
        setTimeout(() => {
          initializeRender();
        }, 1000);
      }
    }
  };

  /**
   * 重新初始化Render实例
   */
  const reinitializeRender = async () => {
    debugLog('🔄 重新初始化Render实例');
    await initializeRender();
    debugLog('reinitializeRender:', render);
  };
  /**
   * 处理搜索功能
   */
  const handleSearch = (searchText: string) => {
    if (render && searchText) {
      render.handleSearch(searchText);
      render.render();
      debugLog('🔍 搜索完成:', searchText);
    } else if (render && !searchText) {
      render.resetMatchOrSearch(false, true);
      render.render();
      debugLog('🧹 搜索已清除');
    }
  };
  const removeQuestion = (question: string, qid: number) => {
    if (render) {
      render.handleQuestion({
        question,
        qid,
        action: 1 // QuestionAction.remove
      });
      debugLog('➖ 问题已删除:', question, qid);
    }
  };
  /**
   * 添加问题
   */
  const addQuestion = (question: string, qid: number) => {
    if (render) {
      render.handleQuestion({
        question,
        qid,
        action: 0 // QuestionAction.add
      });
      debugLog('➕ 问题已添加:', question, qid);
    }
  };

  /**
   * 更新数据而不销毁Render实例
   * 这是一个性能优化的方法，避免频繁重建实例
   */
  const updateData = async () => {
    debugLog('🔄 开始更新Render实例数据（不销毁实例）');

    // 如果实例不存在，则初始化
    if (!render) {
      debugLog('⚠️ Render实例不存在，将进行初始化');
      await initializeRender();
      return;
    }

    // 获取最新的内容数据
    const contentData = getContentData();
    debugLog('📝 更新数据:', contentData);

    // 🔥 关键优化：检查数据是否真的发生了变化
    if (!contentData || (typeof contentData === 'string' && contentData.trim() === '')) {
      debugLog('⚠️ 新数据为空，跳过更新以避免闪烁');
      return;
    }

    try {
      // 如果有setData方法，使用它
      debugLog('🎯 使用Render.setData方法更新数据');
      render.setData(contentData);

      // 重新绑定事件监听器（已优化，避免重复绑定）
      bindEventListeners();

      // 重新处理已有问题
      processExistingQuestions();

      // 🔧 修复：当questionList为空时，强制触发finish事件
      if (!questionList.value || questionList.value.length === 0) {
        debugLog('⚠️ updateData时questionList为空，将强制触发finish事件');
        await nextTick();
        forceFinishEvent();
      }

      debugLog('✅ 数据更新完成（实例未销毁）');
    } catch (error) {
      debugLog('❌ 数据更新失败，回退到重新初始化:', error);
      await reinitializeRender();
    }
  };

  return {
    render,
    destroyRenderInstance,
    initializeRender,
    reinitializeRender,
    updateData,
    processExistingQuestions,
    handleSearch,
    removeQuestion,
    addQuestion
  };
}
