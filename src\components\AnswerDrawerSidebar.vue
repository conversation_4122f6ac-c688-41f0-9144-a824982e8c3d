<template>
  <div>
    <transition name="drawer" appear>
      <div
        class="answer-drawer-wrapper textfont"
        v-if="visible && curQuestion"
        :style="{ zIndex: zIndex }"
      >
        <div class="header">
          <span class="iconfont icon-icon_close" @click="handleClose"></span>
          <div class="title">查看问题</div>
        </div>
        <div class="drawer-body">
          <div class="question">
            <div class="title">
              <div class="creator-name">{{ curQuestion?.userName }}</div>
              的提问
            </div>
            <div class="description">
              <template v-if="curQuestion.questionType != '开放性问题'">
                <div
                  class="keyWords textfont"
                  v-html="'【' + curQuestion.keyword + '】' + curQuestion.questionType + '?'"
                ></div>
              </template>
              <template v-else>
                <div class="keyWords textfont" v-html="'【' + curQuestion.keyword + '】'"></div>
              </template>
              <div class="tags">
                <div v-if="curQuestion.questionNecessity == 1" class="tag1">必要</div>
                <div v-else class="tag1">参考</div>
                <div v-if="curQuestion.questionWeight == 1" class="tag2">私有</div>
                <div v-else class="tag2">公开</div>
              </div>
            </div>
            <div class="bottom">
              <div class="date">
                {{ curQuestion?.createTime }}
              </div>
              <div class="operate">
                <div
                  v-if="curQuestion?.canDelete"
                  class="click-delete"
                  @click="handleDeleteQuestion()"
                >
                  删除
                </div>
              </div>
            </div>
          </div>
          <div class="answer-section" v-if="displayAnswer">
            <div class="answer-card">
              <span v-if="displayAnswer.pinToTop === 1" class="pin-badge">置顶</span>

              <div class="title">
                <span class="name">
                  {{ displayAnswer.userName }}
                </span>
                <span v-if="isAnswererAuthor" class="author-badge">作者</span>
                的回答
              </div>
              <div
                class="klg-list"
                :class="{ collapsed: !isExpanded && shouldShowExpandButton && hasMoreThanFourTags }"
              >
                <div
                  class="klg-item"
                  v-for="(item, index) in displayKnowledgeList"
                  :key="item.code"
                  @click="goToDetail(item.code, item.type)"
                >
                  <el-tooltip placement="top" effect="customized">
                    <template #content>
                      <span v-html="item.title"></span>
                    </template>
                    <div class="klg-tag">
                      <div
                        class="textfont klg-tag-text ellipsis"
                        v-html="removeLineBreaks(item.title)"
                      ></div>
                    </div>
                  </el-tooltip>
                </div>
                <!-- 省略号标签 -->
                <div
                  v-if="!isExpanded && shouldShowExpandButton && hasMoreThanFourTags"
                  class="klg-item ellipsis-tag"
                >
                  <div class="klg-tag ellipsis">
                    <div class="textfont klg-tag-text">...</div>
                  </div>
                </div>
              </div>
              <div class="description">
                <div
                  class="content-text"
                  :class="{ collapsed: !isExpanded && shouldShowExpandButton }"
                  v-html="displayAnswer.answerExplanation"
                ></div>
              </div>
              <div class="bottom flex-space-between">
                <div class="date">
                  {{ displayAnswer.createTime }}
                </div>
                <div v-if="shouldShowExpandButton" class="expand-button" @click="toggleExpand">
                  {{ isExpanded ? '收起' : '展开' }}
                </div>
              </div>
            </div>
            <!-- 回答数量统计 -->
            <div class="answer-count" v-if="totalAnswers > 0">
              <span
                class="count-text"
                :class="{ clickable: totalAnswers > 1 }"
                @click="handleShowAllAnswers"
              >
                共{{ totalAnswers }}条回答
                <el-icon style="transform: translateY(2px)"><CaretBottom /></el-icon>
              </span>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>

  <!-- 浮动问题选择器 - 使用 Teleport 渲染到 body -->
  <Teleport to="body">
    <div
      ref="floatingElement"
      :style="
        floatingVisible ? floatingStyles : { position: 'fixed', top: '-9999px', left: '-9999px' }
      "
      class="floating"
    >
      <Transition name="scale" :duration="{ enter: 200, leave: 50 }">
        <div
          v-if="floatingVisible && questionList && questionList.length > 0"
          class="floating-content"
        >
          <div
            v-for="(question, index) in questionList"
            :key="question.questionId"
            class="floating-content-item flex-center"
            @click="showDrawerWithQuestion(question)"
          >
            <div>
              <span class="keyword-container">
                【
                <span class="keywordList ellipsis keyWords" v-html="question.keyword"></span>
                】
              </span>
              <span v-if="question.questionType != '开放性问题'" class="question-type"
                >{{ question.questionType }} ?
              </span>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </Teleport>

  <!-- 显示所有回答的弹窗 -->
  <el-dialog
    v-model="showAllAnswersDialog"
    width="800px"
    :before-close="handleCloseAllAnswersDialog"
    class="all-answers-dialog"
  >
    <template #header>
      <span class="answer-count-title titlefont">
        {{ totalAnswers > 0 ? `${totalAnswers}条回答` : '回答' }}
      </span>
    </template>

    <div class="dialog-body" v-if="curQuestion">
      <div class="description">
        <template v-if="curQuestion.questionType != '开放性问题'">
          <div
            class="keyWords textfont"
            v-html="'【' + curQuestion.keyword + '】' + curQuestion.questionType + '?'"
          ></div>
        </template>
        <template v-else>
          <div class="keyWords textfont" v-html="'【' + curQuestion.keyword + '】'"></div>
        </template>
        <div class="tags">
          <div v-if="curQuestion.questionNecessity == 1" class="tag1">必要</div>
          <div v-else class="tag1">参考</div>
          <div v-if="curQuestion.questionWeight == 1" class="tag2">私有</div>
          <div v-else class="tag2">公开</div>
        </div>
        <div class="bottom">
          <div class="date">
            {{ curQuestion.createTime }}
          </div>
          <div class="operate">
            <div v-if="curQuestion.canDelete" class="click-delete" @click="handleDeleteQuestion()">
              删除
            </div>
          </div>
        </div>
      </div>
      <el-divider class="divider"></el-divider>

      <div class="all-answer">
        <div v-for="answer in curQuestion.answers" :key="answer.answerId" class="answer-list">
          <div class="answer-card-full">
            <span v-if="answer.pinToTop === 1" class="pin-badge">置顶</span>

            <div class="title">
              <span class="name">
                {{ answer.userName }}
              </span>
              <span v-if="isAnswererAuthorInDialog(answer.userName)" class="author-badge"
                >作者</span
              >
              的回答
            </div>
            <div class="klg-list-full textfont">
              <div
                class="klg-item"
                v-for="item in answer.knowledgeList"
                :key="item.code"
                @click="goToDetail(item.code, item.type)"
              >
                <el-tooltip placement="top" effect="customized">
                  <template #content>
                    <span v-html="item.title"></span>
                  </template>
                  <div class="klg-tag">
                    <div class="klg-tag-text textfont" v-html="item.title"></div>
                  </div>
                </el-tooltip>
              </div>
            </div>
            <div class="description-full" v-html="answer.answerExplanation"></div>
            <div class="bottom textfont">
              <div class="date">
                {{ answer.createTime }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import type { QuestionData } from '@/types/learning';
import { deleteQuestionApi, getQuestionDetailApi } from '@/apis/learning';
import { ref, watch, computed, nextTick, onMounted, onUnmounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { useRouter } from 'vue-router';
import { emitter } from '@/utils/emitter';
import { KlgType } from '@/types';
import { Event } from '@/types/event';
import { CaretBottom } from '@element-plus/icons-vue';
import { useFloating, shift, flip, offset, autoUpdate, size } from '@floating-ui/vue';

// 移除换行符的工具函数
const removeLineBreaks = (text: string): string => {
  if (!text) return '';
  // 移除所有类型的换行符和多余的空白字符
  return text
    .replace(/[\r\n\t]+/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
};

const drawerControllerStore = useDrawerControllerStore();
const { questionId } = storeToRefs(drawerControllerStore);
const router = useRouter();
// 定义props
interface Props {
  visible?: boolean;
  questionData?: any; // 可以是问题数据对象或HTML元素
  projectAuthor?: string; // 项目作者
  zIndex?: number;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  questionData: null,
  projectAuthor: '',
  zIndex: 1001
});

const emits = defineEmits(['close', 'show-question']);

const curQuestion = ref<any>(null);
const questionList = ref<any[]>();

// 展开/收起状态
const isExpanded = ref(false);

// 显示所有回答弹窗的状态
const showAllAnswersDialog = ref(false);

// floating-ui 相关状态
const referenceElement = ref<HTMLElement | null>(null);
const floatingElement = ref<HTMLElement | null>(null);
const floatingVisible = ref(false);
const { floatingStyles } = useFloating(referenceElement, floatingElement, {
  open: floatingVisible,
  placement: 'top-start', // 改为 top-start 以更好地对齐
  strategy: 'fixed', // 使用 fixed 定位策略，确保相对于视口定位
  middleware: [
    offset(8), // 增加偏移量，使浮动内容不会紧贴参考元素
    flip({
      fallbackPlacements: ['bottom-start', 'top-end', 'bottom-end'], // 更多的回退位置选项
      padding: 10 // 与视口边缘保持10px的距离
    }),
    shift({
      padding: 10, // 确保内容不会太靠近视口边缘
      crossAxis: true // 允许在交叉轴上移动
    }),
    size({
      apply({ availableWidth, availableHeight, elements }) {
        // 限制浮动元素的最大宽度和高度，并确保z-index足够高
        Object.assign(elements.floating.style, {
          maxWidth: `${Math.min(400, availableWidth)}px`,
          maxHeight: `${Math.min(300, availableHeight)}px`,
          zIndex: '10000'
        });
      },
      padding: 10 // 与视口边缘保持10px的距离
    })
  ],
  whileElementsMounted: autoUpdate // 自动更新位置
});

// 计算要显示的回答：优先显示置顶回答，否则显示第一个回答
const displayAnswer = computed(() => {
  if (!curQuestion.value?.answers || curQuestion.value.answers.length === 0) {
    return null;
  }

  // 查找置顶回答 (pinToTop === 1)
  const pinnedAnswer = curQuestion.value.answers.find((answer: any) => answer.pinToTop === 1);

  // 如果有置顶回答则返回置顶回答，否则返回第一个回答
  return pinnedAnswer || curQuestion.value.answers[0];
});

// 计算总回答数量
const totalAnswers = computed(() => {
  return curQuestion.value?.answers?.length || 0;
});

// 判断知识点标签是否超过4个
const hasMoreThanFourTags = computed(() => {
  return displayAnswer.value?.knowledgeList?.length > 4;
});

// 判断内容是否超过4行（估算：每行约50个字符）
const hasMoreThanFourLines = computed(() => {
  if (!displayAnswer.value?.answerExplanation) return false;
  // 简单估算：每行约50个字符，4行约200个字符
  return displayAnswer.value.answerExplanation.length > 200;
});

// 判断是否需要显示展开按钮（知识点超过4个 OR 内容超过4行）
const shouldShowExpandButton = computed(() => {
  return hasMoreThanFourTags.value || hasMoreThanFourLines.value;
});

// 计算要显示的知识点列表
const displayKnowledgeList = computed(() => {
  if (!displayAnswer.value?.knowledgeList) return [];

  // 如果展开状态或不需要展开按钮，显示所有标签
  if (isExpanded.value || !shouldShowExpandButton.value) {
    return displayAnswer.value.knowledgeList;
  }

  // 如果知识点超过4个且未展开，只显示前4个
  if (hasMoreThanFourTags.value) {
    return displayAnswer.value.knowledgeList.slice(0, 4);
  }

  // 否则显示所有标签
  return displayAnswer.value.knowledgeList;
});

// 判断当前显示的回答者是否为项目作者
const isAnswererAuthor = computed(() => {
  if (!props.projectAuthor || !displayAnswer.value?.userName) {
    return false;
  }
  return props.projectAuthor === displayAnswer.value.userName;
});

// 判断指定回答者是否为项目作者 - 用于弹窗中的所有回答
const isAnswererAuthorInDialog = (answerUserName: string) => {
  if (!props.projectAuthor || !answerUserName) {
    return false;
  }
  return props.projectAuthor === answerUserName;
};

// 处理关闭
const handleClose = () => {
  emits('close');
};

// 切换展开/收起状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

// 显示所有回答
const handleShowAllAnswers = () => {
  if (totalAnswers.value > 1) {
    // 显示内部弹窗
    showAllAnswersDialog.value = true;
  }
};

// 关闭所有回答弹窗
const handleCloseAllAnswersDialog = () => {
  showAllAnswersDialog.value = false;
};

// 处理删除问题
const handleDeleteQuestion = async () => {
  if (curQuestion.value?.questionId) {
    emitter.emit(Event.REMOVE_QUESTION, [
      curQuestion.value.questionId,
      curQuestion.value.associatedWords
    ]);
    handleClose();
    handleCloseAllAnswersDialog();
  }
};

// 跳转到详情页
const goToDetail = (code: string, type: KlgType) => {
  if (type == KlgType.knowledge) {
    const { href } = router.resolve({
      path: '/klgdetail',
      query: {
        klgCode: code
      }
    });
    window.open(href, '_blank');
  } else if (type == KlgType.area) {
    const { href } = router.resolve({
      path: '/area',
      query: {
        areaCode: code
      }
    });
    window.open(href, '_blank');
  }
};

// 关闭浮动内容
const closeFloating = () => {
  // 立即隐藏浮动内容，避免过渡动画导致位置变化
  if (floatingElement.value) {
    // 先将元素设置为不可见，然后再改变状态
    floatingElement.value.style.visibility = 'hidden';
    // 使用 setTimeout 确保视觉上立即隐藏，然后再改变状态
    setTimeout(() => {
      floatingVisible.value = false;
      // 重置可见性，以便下次显示
      if (floatingElement.value) {
        floatingElement.value.style.visibility = '';
      }
    }, 0);
  } else {
    floatingVisible.value = false;
  }
};

// 点击事件处理函数 - 使用捕获阶段
const handleDocumentClick = (event: MouseEvent) => {
  // 如果悬浮内容未打开，不需要处理
  if (!floatingVisible.value) return;

  // 检查点击是否在浮动元素内
  const isClickInFloating = floatingElement.value?.contains(event.target as Node);

  // 如果点击不在浮动元素内，则关闭浮动内容
  if (!isClickInFloating) {
    closeFloating();
  }
};

// 显示抽屉并设置问题
const showDrawerWithQuestion = async (question: any) => {
  // 关闭浮动内容
  closeFloating();

  // 等待浮动内容完全关闭后再设置问题，确保 AnswerDrawerSidebar 正确显示
  await nextTick();

  // 设置当前问题
  curQuestion.value = question;

  // 通知父组件显示 AnswerDrawerSidebar
  emits('show-question', question);
};

// 监听questionData变化
watch(
  () => props.questionData,
  async (newVal) => {
    if (newVal) {
      // 检查是否是HTML元素
      if (newVal instanceof HTMLElement) {
        // 从HTML元素获取问题ID
        const qids = newVal.getAttribute('data-qid');
        if (qids) {
          const qidArray = qids.split(',');

          // 无论有几个问题，都先显示浮动选择器
          try {
            // 创建一个问题列表数组
            questionList.value = [];

            // 对每个问题ID单独发起请求
            const promises = qidArray.map((qid) => getQuestionDetailApi(qid));
            const results = await Promise.all(promises);

            // 合并所有问题数据
            results.forEach((res) => {
              if (res.data && res.data.length > 0) {
                questionList.value!.push(res.data[0]);
              }
            });

            // 显示浮动内容让用户选择（无论单个还是多个问题）
            // 确保 referenceElement 是有效的 DOM 元素
            if (newVal instanceof HTMLElement) {
              referenceElement.value = newVal;

              // 先关闭浮动内容，确保重新定位
              floatingVisible.value = false;

              // 使用 nextTick 确保 DOM 更新后再显示浮动内容
              await nextTick();

              // 短暂延迟后显示浮动内容，确保 floating-ui 能正确计算位置
              setTimeout(() => {
                floatingVisible.value = true;
              }, 50);
            } else {
              console.warn('referenceElement is not a valid HTMLElement:', newVal);
            }
          } catch (error) {
            console.error('获取问题详情失败:', error);
          }
        }
      } else {
        // 如果是问题数据对象，直接使用（这种情况下显示 AnswerDrawerSidebar）
        curQuestion.value = newVal;
      }
    } else {
      // 如果 questionData 为空，清空当前问题和浮动状态
      curQuestion.value = null;
      floatingVisible.value = false;
      questionList.value = [];
    }
  },
  { immediate: true }
);

// 监听questionId变化（从store）
watch(
  () => questionId.value,
  async (newVal) => {
    if (newVal) {
      try {
        const res = await getQuestionDetailApi(newVal);
        if (res.data && res.data.length > 0) {
          curQuestion.value = res.data[0];
        }
        questionId.value = '';
      } catch (error) {
        console.error('获取问题详情失败:', error);
      }
    }
  }
);

// 生命周期钩子
onMounted(() => {
  // 添加全局点击事件监听 - 使用捕获阶段
  document.addEventListener('click', handleDocumentClick, true);
});

onUnmounted(() => {
  // 移除事件监听器，注意第三个参数需要与添加时一致
  document.removeEventListener('click', handleDocumentClick, true);
});
</script>

<style scoped lang="less">
// 弹窗动画效果
.drawer-enter-active,
.drawer-leave-active {
  transition: all 0.3s ease;
}

.drawer-enter-from,
.drawer-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.drawer-enter-to,
.drawer-leave-from {
  opacity: 1;
  transform: translateX(0);
}

.answer-drawer-wrapper {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: calc(100vh - 110px);
  background-color: white;
  border-left: 1px solid #e8e8e8;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 10px;
  box-sizing: border-box;
  color: #333333;
  font-family:
    '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
  font-size: 12px;
  font-weight: 400;

  .header {
    position: relative;
    margin-bottom: 10px;

    .icon-icon_close {
      position: absolute;
      right: 0;
      top: 0;
      cursor: pointer;
      font-size: 16px;
      color: #666;

      &:hover {
        color: #333;
      }
    }

    .title {
      font-weight: 700;
      font-size: 16px;
    }
  }

  .drawer-body {
    width: 260px;
    max-height: 450px;
    overflow-y: auto;
    padding: 10px;
    margin: 0 auto;
    margin-top: 20px;
    border-width: 1px;
    border-style: solid;
    background-color: rgba(255, 255, 255, 1);
    box-sizing: border-box;
    border-color: rgba(228, 228, 228, 1);
    box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.0980392156862745);
    border-radius: 4px;
    font-weight: 400;

    .question {
      display: flex;
      flex-direction: column;
      align-items: left;
      margin-bottom: 15px;

      .title {
        margin-top: 10px;
        display: flex;
        justify-content: left;
        font-size: 12px;

        .creator-name {
          font-weight: 700;
          margin-right: 10px;
        }
      }

      .description {
        margin-top: 10px;
        margin-left: 5px;
        font-size: 14px;

        align-items: center;
        width: 100%;
        .keyWords {
          max-width: 67%;
        }

        .question-type {
          display: inline-block;
          margin-left: 0;
        }

        span {
          display: inline-block;
        }
      }
    }

    .answer-section {
      flex: 1;
      overflow-y: auto;
      border-left: 1px solid #e8e8e8;
      padding: 0 10px;
      margin-bottom: 10px;

      .answer-card {
        border-radius: 4px;

        .title {
          margin-top: 5px;
          display: flex;
          align-items: center;
          gap: 8px;

          .name {
            font-weight: 700;
          }
        }

        .description {
          font-size: 14px;
          margin-top: 15px;
          margin-bottom: 10px;
          line-height: 1.5;

          .content-text {
            transition: all 0.3s ease;
            line-height: 1.5;

            &.collapsed {
              /* 限制显示4行，每行约1.5em高度 */
              max-height: 6em; /* 4行 × 1.5em */
              overflow: hidden;
              position: relative;
              display: -webkit-box;
              -webkit-line-clamp: 4;
              line-clamp: 4;
              -webkit-box-orient: vertical;
            }
          }
        }
      }

      .answer-count {
        margin-top: 10px;
        .count-text {
          color: #1661ab;
          cursor: pointer;
          font-size: 12px;

          &:hover {
            color: #0d4a73;
            font-weight: 500;
          }
        }
      }
    }
  }
}
.bottom {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  .date {
    font-size: 12px;
    color: #999999;
  }

  .operate {
    display: flex;
    flex-direction: row;

    .click-delete {
      color: var(--color-deep);
      &:hover {
        font-weight: 700;
        cursor: pointer;
      }
      margin: 0 10px;
    }
  }
}
// 所有知识点
.klg-list {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  width: 205px;

  .klg-item {
    width: 100px;
    height: 25px;
    padding: 0 5px;
    background-color: rgba(242, 242, 242, 1);
    border: none;
    border-radius: 15px;
    &:hover {
      cursor: pointer;
    }
    .klg-tag {
      background-color: #f2f2f2;
      border-radius: 10px;
      width: 100%;
      margin: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #333333;
      text-align: center;
      .klg-tag-text {
        height: 100%;
        line-height: 25px;
        font-size: 12px;
      }
    }
  }
}

// 所有回答弹窗样式
:deep(.el-dialog) {
  padding: 20px;
  height: 720px;
  width: 800px;
  overflow-y: auto;
}

.answer-count-title {
  font-size: 16px;
  font-weight: 600;
}

.all-answers-dialog {
  .dialog-body {
    color: #333333;
    font-family:
      '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
    font-size: 12px;
    font-weight: 400;

    .question {
      display: flex;
      flex-direction: column;
      align-items: left;
      margin-bottom: 10px;
      margin-top: -10px;

      .title {
        margin-top: 10px;
        display: flex;
        justify-content: left;
        font-size: 12px;

        .creator-name {
          font-weight: 700;
          margin-right: 10px;
        }
      }

      .description {
        margin-top: 10px;
        font-size: 14px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-left: 10px;

        .keyWords {
          display: inline-block;
          :deep(p) {
            display: inline;
            margin: 0;
            padding: 0;
          }
        }

        .question-type {
          display: inline-block;
          margin-left: 0;
        }

        span {
          display: inline-block;
        }
      }

      .bottom {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;

        .date {
          font-size: 12px;
          color: #999999;
        }

        .operate {
          display: flex;
          flex-direction: row;

          .click-delete {
            color: var(--color-deep);
            &:hover {
              font-weight: 700;
              cursor: pointer;
            }
            margin: 0 10px;
          }
        }
      }
    }

    .all-answer {
      margin-top: 15px;

      .answer-list {
        margin-left: 10px;
        .answer-card-full {
          padding: 5px;
          border-radius: 4px;
          border-bottom: 1px solid #e8e8e8;

          .title {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;

            .name {
              font-weight: 700;
            }
          }

          .klg-list-full {
            .klg-list();
            margin: 10px 0;
            display: flex;
            flex-wrap: wrap;
            width: 100%;
            gap: 5px;
          }

          .description-full {
            font-size: 14px;
            line-height: 1.5;
            margin: 10px 0;
          }
        }
      }
    }
  }
}
// 日期
.bottom {
  margin-bottom: 5px;
  .date {
    color: #666666;
    font-size: 12px;
  }
}
.author-badge {
  color: #ecc32f;
  border: 1px solid;
  font-size: 12px;
  padding: 2px 6px;
}
/* 自定义滚动条样式 */
&::-webkit-scrollbar {
  width: 6px;
  height: 10px !important;
  border-radius: 5px;
  display: block;
}

&::-webkit-scrollbar-thumb {
  width: 6px;
  height: 10px !important;
  background-color: #dcdfe6 !important;
  border-radius: 2px;
}
//表格
table {
  border: 1px solid #ddd !important;
  border-collapse: collapse;
  background-color: #fff;
}

thead {
  background-color: #f5f5f5;
}

tr {
  border-bottom: 1px solid #eee;
}

th,
td {
  padding: 8px 12px;
  text-align: left;
  border-right: 1px solid #eee;
  vertical-align: top;
}

th {
  font-weight: bold;
  background-color: #f0f0f0;
  color: #333;
}

.floating-content {
  padding: 10px 10px;
  background: var(--color-theme-project);
  color: white;
  display: flex;
  justify-content: center;
  flex-direction: column;
  border-radius: 5px;
  font-family:
    '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
  font-size: 14px;
  font-weight: 400;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  max-width: 100%;
  min-width: 150px;
  animation: fadeIn 0.2s ease-in-out;
  z-index: 10000;
  .floating-content-item {
    max-width: 400px;
    max-height: 50px;

    &:hover {
      font-weight: bold;
      cursor: pointer;
      transform: translateX(2px);
    }
    .keyword-container {
      display: inline-flex;
      align-items: center;
      white-space: nowrap;
      max-width: 320px;
      margin-bottom: 10px;
      line-height: 20px;

      .keywordList {
        font-weight: bold;
        max-width: 300px;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
