import { defineStore } from 'pinia';
import { type PrjInfo, PrjForm, PrjType } from '@/types/project';

export const useLearningStore = defineStore('learning', {
  state: () => ({
    validIndex: 0,
    prjId: 0,
    prjForm: PrjForm.video,
    prjType: PrjType.klgExplain,
    chapterList: [],
    written: false,
    uniqueCode: '',
    contentId: '',
    chapterId: '',
    autoPay: false,
    mounted: false,
    clickLockAtGoodContent: false
  }),
  getters: {},
  actions: {
    setInfo(info) {
      this.validIndex = info.validIndex;
      this.prjId = info.prjId;
      this.prjForm = info.prjForm;
      this.prjType = info.prjType;
      this.chapterList = info.chapterList;
      this.uniqueCode = info.uniqueCode;
      this.written = true;

      // 设置当前章节ID - 从当前有效章节获取
      if (info.chapterList && info.chapterList.length > 0 && typeof info.validIndex === 'number') {
        const currentChapter = info.chapterList[info.validIndex];
        if (currentChapter && currentChapter.chapterId) {
          this.chapterId = String(currentChapter.chapterId);
          console.log('learningStore - 设置chapterId:', this.chapterId);
        }
      }
    }
  }
});
