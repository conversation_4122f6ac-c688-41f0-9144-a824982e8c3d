<template>
  <!-- 
    将页面下方的历史记录部分抽离组件
    负责实现历史记录模块的所有功能
   -->
  <div class="learn-down">
    <div class="up-title">
      <div class="left-title">
        <img
          style="width: 18px; height: 18px; margin-right: 10px"
          src="@/assets/images/learn/u3865.svg"
          alt="history"
        />
        浏览历史
      </div>
      <div class="right-btns">
        <div
          :class="currentIndex == index ? 'btn-active' : 'btns'"
          class="btns"
          v-for="(label, index) in btnsData"
          :key="'btn' + index"
          @click="selectData(index)"
        >
          {{ label }}
        </div>
        <div class="btns" style="width: 100px" @click="clearHistoryVisible = true">
          清除浏览历史
        </div>
        <el-input
          v-model="historyKey"
          style="width: 225px; height: 30px"
          placeholder="请搜索浏览历史"
          size="small"
          @keyup.enter="keyForHistory"
        >
          <template #suffix>
            <el-icon @click="keyForHistory" style="cursor: pointer" class="el-input__icon">
              <search />
            </el-icon>
          </template>
        </el-input>
      </div>
    </div>
    <div class="mid-content">
      <BrowseHistory
        ref="browseHistory"
        v-for="(hisItem, index) in historyInfo"
        :key="index + '_'"
        :history="hisItem"
        :showDate="showDates[index]"
        @afterDelete="getHistory"
      ></BrowseHistory>
    </div>
    <!-- 后续优化 数据加载完成后提示信息变更  有问题 -->
    <div v-if="historyTotal == 0" style="width: 100%; display: flex; justify-content: center">
      <el-empty description="暂无浏览记录" />
    </div>

    <div v-else-if="historyCurPage != historyTotalPage" class="down-more">
      <span class="footerBtn" @click="getMore">
        <span class="myicon"></span>
        加载更多
      </span>
    </div>
    <!-- <div v-else-if="historyCurPage != historyTotalPage" class="down-more" @click="getMore">
      加载更多
    </div> -->

    <div
      v-else
      v-if="historyTotalPage != 1"
      style="width: 100%; display: flex; justify-content: center; color: var(--color-theme-project)"
    >
      暂无更多数据
    </div>
  </div>

  <el-dialog class="dg" v-model="clearHistoryVisible" :show-close="false" width="526px" top="40vh">
    <template #header>
      <div class="dg-header">
        <h1>清除浏览历史</h1>
        <img
          @click="clearHistoryVisible = !clearHistoryVisible"
          style="width: 16px; height: 16px; cursor: pointer; float: right"
          src="@/assets/images/prjlearn/close.svg"
          alt=""
        />
      </div>
    </template>
    <div class="content">
      <img src="@/assets/images/prjlearn/process_warn.svg" alt="" />
      确认清除后，数据将无法恢复。是要清除所有的浏览历史吗？
    </div>
    <template #footer>
      <div class="foot-btns">
        <CmpButton class="btn" type="info" @click="clearHistoryVisible = !clearHistoryVisible"
          >关闭窗口</CmpButton
        >
        <CmpButton class="btn" type="primary" @click="deleteAll">确认清除</CmpButton>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getHistoryListApi } from '@/apis/learnStatistics';
import BrowseHistory from './BrowseHistory.vue';
import { Search } from '@element-plus/icons-vue';
//底部浏览历史筛选按钮
const btnsData = ['全部', '近一月', '近一周', '今天'];
const showDates = ref<boolean[]>([]);
let currentIndex = ref(0);

const isScroll = ref(false);
const selectData = (index: number) => {
  if (index == currentIndex.value) {
    return;
  }
  currentIndex.value = index;
  // 重置分页变量
  historyCurPage.value = 1;
  //搜索内容数组清空
  historyInfo.value.splice(0, historyInfo.value.length);
  isScroll.value = true;
  getHistory();
};
const historyCurPage = ref(1);
const historyTotal = ref(3);
const historyInfo = ref([]);
const historyLimit = ref(10);
const historyTotalPage = ref(1);
// 获取
const getMore = () => {
  // 使用增加limit的方法 实现加载更多的展示
  historyCurPage.value += 1;
  // historyLimit.value += 10;
  // if (historyLimit.value + 10 > historyTotal.value) {
  //   historyLimit.value = historyTotal.value;
  // } else {
  //   historyLimit.value += 10;
  // }
  isScroll.value = false;
  getHistory();
};

// 删除所有
const browseHistory = ref(null);
const deleteAll = () => {
  clearHistoryVisible.value = false;
  if (browseHistory.value != null) {
    //@ts-ignore
    browseHistory.value[0].delHistory('all');
  } else {
    ElMessage.warning('历史记录为空~');
  }
};
// 历史记录搜索关键字
const historyKey = ref('');

// 清除浏览历史弹窗设置
const clearHistoryVisible = ref(false);

//含关键字的历史记录搜索
const keyForHistory = () => {
  historyInfo.value.splice(0, historyInfo.value.length);
  isScroll.value = true;
  getHistory();
};
// 获取历史记录
const currentDate = ref<string>();

const getHistory = () => {
  const params = {
    current: historyCurPage.value,
    limit: historyLimit.value,
    title: historyKey.value,
    time: currentIndex.value
  };
  getHistoryListApi(params)
    .then((res) => {
      // 此处对类型进行判断
      historyTotal.value = res.data.total;
      historyTotalPage.value = res.data.totalPage;

      for (let i = 0; i < res.data.list.length; i++) {
        historyInfo.value.push(res.data.list[i]);
        //console.log('------', res.data.list[i]);
        if (currentDate.value == undefined) {
          showDates.value.push(true);
          currentDate.value = res.data.list[i].date;
        } else {
          if (currentDate.value == res.data.list[i].date) {
            showDates.value.push(false);
          } else {
            showDates.value.push(true);
            currentDate.value = res.data.list[i].date;
          }
        }
      }
      //console.log('------');
      console.log(res);
      if(isScroll.value) {
        nextTick(()=>{
        window.scrollTo({ top: 500, behavior: 'smooth' });
      })
      }
    })
    .catch((error) => {
      console.log(error);
    });
};
onMounted(() => {
  //   getList();
  isScroll.value = false
  getHistory();
});
</script>

<style scoped lang="less">
.learn-down {
  padding-top: 34px;
  width: var(--width-fixed--project);
  padding-bottom: 30px;
  background-color: white;

  .up-title {
    width: 100%;
    display: flex;
    height: 30px;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;

    .left-title {
      width: 10%;
      height: 100%;
      display: flex;
      align-items: center;
    }

    .right-btns {
      width: 48%;
      height: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .btns {
        height: 100%;
        width: 70px;
        border-radius: 4px;
        border-style: solid;
        border-color: var(--color-theme-project);
        border-width: 0.8px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--color-theme-project);
        font-size: var(--fontsize-middle-project);
        cursor: pointer;
      }

      .btns:hover {
        background-color: var(--color-second);
        font-weight: bolder;
      }

      .btn-active {
        background-color: var(--color-theme-project);
        color: white;
      }

      .btn-active:hover {
        background-color: var(--color-theme-project);
        color: white;
        font-weight: bolder;
      }
    }
  }

  .mid-content {
    margin-top: 30px;
  }

  .down-more {
    width: 100%;
    display: flex;
    justify-content: center;
    text-align: center;
    color: var(--color-theme-project);
    font-family: var(--text-family);
    font-weight: 400;
    font-size: 14px;
    height: 20px;
    line-height: 20px;
    margin: 10px 0;
    cursor: pointer;
    .footerBtn {
      width: 90px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .myicon {
        width: 14px;
        height: 12px;
        margin-right: 5px;
        background-image: url('@/assets/images/project/u3964.svg');
      }
      &:hover {
        font-weight: bold;
      }
    }
  }

  .down-more:hover {
    font-weight: bolder;
  }

  .dg {
    .dg-header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      border-bottom: 2px solid #f2f2f2;
      padding-bottom: 15px;

      h1 {
        font-size: 18px;
        font-weight: 700;
        color: var(--color-theme-project);
      }
    }
  }

  .content {
    width: 100%;
    white-space: pre-wrap;
  }
}

.foot-btns {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;

  .btn {
    width: 160px;
    height: 43px;

    &:nth-child(2) {
      margin-left: 20px;
    }
  }
}
</style>
