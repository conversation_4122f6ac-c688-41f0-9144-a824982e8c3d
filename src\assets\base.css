/* color palette from <https://github.com/vuejs/theme> */
:root {
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #f8f8f8;
  --vt-c-white-mute: #f2f2f2;

  --vt-c-black: #181818;
  --vt-c-black-soft: #222222;
  --vt-c-black-mute: #282828;

  --vt-c-indigo: #2c3e50;

  --vt-c-divider-light-1: rgba(60, 60, 60, 0.29);
  --vt-c-divider-light-2: rgba(60, 60, 60, 0.12);
  --vt-c-divider-dark-1: rgba(84, 84, 84, 0.65);
  --vt-c-divider-dark-2: rgba(84, 84, 84, 0.48);

  --vt-c-text-light-1: var(--vt-c-indigo);
  --vt-c-text-light-2: rgba(60, 60, 60, 0.66);
  --vt-c-text-dark-1: var(--vt-c-white);
  --vt-c-text-dark-2: rgba(235, 235, 235, 0.64);

  /* --color-theme-project: #005579;*/
  --color-black: #333333;
  --color-deep: #666666;
  --color-grey: #999999;
  --color-theme-project: #1973cb; /* 主题颜色 */
  --color-second: #d6e9f6;
  --color-pay: #ffd37a;
  --color-inactive-project: #d7d7d7;
  --width-fixed--project: 1400px; /* 很多地方宽度定为1400px，左右padding设置为10px */
  --padding-box: 10px;
  --fontsize-small-project: 12px;
  --fontsize-middle-project: 14px; /*字号最小12，正文14，标题16向上每两个px一个层级 */
  --fontsize-large-project: 16px;
  /* --title-family: '黑体', sans-serif; */
  /* --text-family: '华文细黑', 'STXihei', sans-serif; */
  --title-family:
    '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0',
    sans-serif;
  --text-family:
    '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;

  --fontsize-large-header: 28px;
  --fontsize-primary-header: 18px;
  --fontsize-small-header: 12px;
  --fontsize-mini-header: 10px;

  --color-back-header: #d6e9f6;
  --shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
}

/* semantic color variables for this project */
:root {
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute);

  --color-border: var(--vt-c-divider-light-2);
  --color-border-hover: var(--vt-c-divider-light-1);

  --color-heading: var(--vt-c-text-light-1);
  --color-text: var(--vt-c-text-light-1);

  --section-gap: 160px;
}

.bigbox {
  height: 100%;
}

