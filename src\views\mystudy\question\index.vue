<!-- waqtodo 此处可以跳转详情页，等后端接口改好后联调 -->
<template>
  <div class="collect-container">
    <div class="collect-header">
      <span class="question-title" @click="goBack">
        <img style="height: 10px; width: 10px" src="@/assets/images/myStudy/back.svg" />
        返回个人空间
      </span>
      <div class="collect">
        <el-input
          v-model="searchInput"
          class="w-50 m-2"
          placeholder="请输入关键字/材料名称"
          :prefix-icon="Search"
          @keyup.enter="searchFn"
          clearable
        />
      </div>
    </div>
    <div v-if="questionlist.length != 0">
      <div class="question-middle">
        学习中我提了{{ myQuestions }}个问题，已经有{{ repliedQuestions }}个问题被回复
      </div>
      <div class="question-content">
        <div
          class="question-content-item"
          v-for="(item, index) in questionlist"
          :key="index"
          @click="goToQuestionDetail(item)"
        >
          <div class="question-content-item-left">
            <div class="question-content-item-top">
              <span class="question-content-item-top-left">
                <span class="quote-content-wrapper">
                  <span class="quote-left">"</span>
                  <el-tooltip
                    placement="top"
                    :content="(item.associatedWords)"
                    :raw-content="true"
                    :show-after="200"
                    effect="customized"
                  >
                    <span
                      class="ellipsis-text"
                      v-html="(item.associatedWords)"
                    ></span>
                  </el-tooltip>
                  <span class="quote-right">"</span>
                </span>
              </span>
              <span class="question-content-item-top-right" v-html="item.createTime"></span>
            </div>
            <div class="question-content-item-bottom">
              <span class="keyword">
                <span class="quote-content-wrapper">
                  <span class="quote-left">[</span>
                  <el-tooltip
                    placement="top"
                    :content="
                      item.questionType === '开放性问题'
                        ? (item.questionDescription)
                        : (item.keyword)
                    "
                    :raw-content="true"
                    :show-after="200"
                    effect="customized"
                  >
                    <span
                      class="ellipsis-text"
                      v-html="
                        item.questionType === '开放性问题'
                          ? (item.questionDescription)
                          : (item.keyword)
                      "
                    ></span>
                  </el-tooltip>
                  <span class="quote-right">]</span>
                </span>
              </span>

              <span class="requesType">
                <span
                  v-if="item.questionType !== '开放性问题'"
                  v-html="item.questionType + '?'"
                ></span>
                <span v-else>开放性问题</span>
              </span>
              <span class="requestNum">
                <span>(</span>
                <span v-html="item.repliedNumber"></span>
                <span>)</span>
              </span>
            </div>
          </div>
          <div class="question-content-item-right">
            <div class="question-content-item-img">
              <img :src="item.coverPic" />
            </div>
            <el-tooltip
              placement="top"
              :content="(item.title)"
              :raw-content="true"
              :show-after="200"
            >
              <div
                class="fontstyle4 ellipsis-text title"
                v-html="(item.title)"
              ></div>
            </el-tooltip>
          </div>
        </div>
      </div>
      <div class="question-bottom">
        <div class="down-more" @click="getMore" v-if="shouldMore">加载更多</div>
        <div class="no-more" v-if="!shouldMore">已经是最底部啦</div>
        <!-- TODO：这个组件是无效的，或许是backtop? -->
        <FindTop></FindTop>
      </div>
    </div>
    <div v-else class="lc-empty"><el-empty description="暂无提问记录" /></div>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { getMyquestionlistApi } from '@/apis/mystudy';
import type { ResponseData, QuestionListData } from '@/apis/mystudy';
import { ref } from 'vue';

const router = useRouter();
function goBack() {
  router.push('/userspace');
}
// 使用从API导入的类型，不需要重复定义
type questionData = QuestionListData['records'][0];
let searchInput = ref('');
// 获取我的学习提问
const limit = 10;
const current = ref(1);
let keywordOrTitle = ref('');
const questionlist = ref<questionData[]>([]); // 订单列表
const myQuestions = ref(0); // 我提问的总个数
const repliedQuestions = ref(0); // 被回复的总个数
let shouldMore = ref(true);
const getQuestionlist = (current: number, limit: number, keywordOrTitle: string) => {
  getMyquestionlistApi(current, limit, keywordOrTitle).then(
    (res: ResponseData<QuestionListData>) => {
      myQuestions.value = res.data.questionCount;
      repliedQuestions.value = res.data.repliedCount;
      questionlist.value = res.data.records;
      shouldMore.value = current < parseInt(res.data.total) / 10 + 1;
    }
  );
};

// 初始化
const InitData = () => {
  current.value = 1;
  keywordOrTitle.value = '';
  getQuestionlist(current.value, limit, keywordOrTitle.value);
};

InitData();

// 加载更多
const getMore = () => {
  current.value += 1;
  getMyquestionlistApi(current.value, limit, keywordOrTitle.value).then(
    (res: ResponseData<QuestionListData>) => {
      myQuestions.value = res.data.questionCount;
      repliedQuestions.value = res.data.repliedCount;
      questionlist.value = questionlist.value.concat(res.data.records);
      shouldMore.value = current.value < parseInt(res.data.total) / 10 + 1;
    }
  );
};

// 根据订单编号等搜索
const searchFn = () => {
  current.value = 1;
  keywordOrTitle.value = searchInput.value;
  getQuestionlist(current.value, limit, keywordOrTitle.value);
};

// 跳转到问题详情页（在新窗口中打开）
const goToQuestionDetail = (item: questionData) => {
  const url = router.resolve({
    path: '/questionDetail',
    query: {
      spuId: item.spuId,
      chapterId: item.chapterId,
      questionId: item.questionId
    }
  });
  window.open(url.href, '_blank');
};
</script>

<style lang="less" scoped>
@titlefontsize: 18px;
@titlefontfamily: '黑体';
@headerfontfamily: '华文细黑';
@headerfontsize: 14px;
@margin: 10px;
@bgclor: #eee;

.fontstyle1 {
  font-size: 14px;
}

.fontstyle2 {
  font-size: 12px;
}

.fontstyle3 {
  font-size: 16px;
  font-weight: 700;
}

.fontstyle4 {
  font-size: 14px;
  font-weight: 700;
}

.collect-container {
  // 重写element样式
  --el-color-primary: var(--color-theme-project);

  display: flex;
  flex-direction: column;
  width: 1090px;
  padding-left: 2 * @margin;
  padding-top: 13px;

  .collect-header {
    display: flex;
    justify-content: space-between;

    .question-title {
      font-family: @titlefontfamily;
      font-size: @headerfontsize;
      color: var(--color-theme-project);
    }

    .question-title:hover {
      font-weight: 700;
      cursor: pointer;
    }

    .collect {
      width: 225px;
    }
  }

  .question-middle {
    font-size: 12px;
  }

  .question-content {
    margin-top: 18px;
    margin-bottom: 44px;
    width: 100%;

    .question-content-item {
      width: 100%;
      height: 140px; /* 减小整体高度 */
      box-shadow: 0px 5px 5px #eee;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;
      overflow: hidden;
      cursor: pointer; /* 添加手型光标，提示可点击 */
      transition: all 0.2s ease; /* 添加过渡效果 */

      &:hover {
        background-color: #f9f9f9; /* 鼠标悬停时的背景色 */
        transform: translateY(-2px); /* 轻微上浮效果 */
        box-shadow: 0px 8px 8px #ddd; /* 增强阴影效果 */
      }

      .question-content-item-left {
        padding-top: 10px;
        padding-left: 25px;
        width: 75%;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .question-content-item-top {
          margin-bottom: 1px;
          display: flex;
          flex-direction: row;

          .question-content-item-top-left {
            display: flex;
            flex-direction: row;
            .fontstyle1();
            width: 72%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            min-height: 50px; /* 减小高度 */
            line-height: 1.5; /* 使用相对行高 */
            align-items: center; /* 垂直居中 */
          }

          .question-content-item-top-right {
            .fontstyle2();
            flex: 1;
            height: 50px; /* 减小高度 */
            line-height: 1.5; /* 使用相对行高 */
            display: flex;
            align-items: center;
          }
        }

        .question-content-item-bottom {
          display: flex;
          .keyword {
            display: flex;
            flex-direction: row;
            height: 50px; /* 减小高度 */
            line-height: 1.5; /* 使用相对行高 */
            width: 72%;
            .fontstyle3();
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            align-items: center; /* 垂直居中 */
          }

          .requesType {
            width: 12%;
            height: 50px; /* 减小高度 */
            line-height: 1.5; /* 使用相对行高 */
            display: flex;
            align-items: center;
          }
          .requestNum {
            height: 50px; /* 减小高度 */
            line-height: 1.5; /* 使用相对行高 */
            display: flex;
            align-items: center;
          }
        }
      }

      .question-content-item-right {
        display: flex;
        align-items: center;
        flex: 1;
        height: 100%;
        width: 100%;

        .question-content-item-img {
          margin-right: 10px;

          img {
            width: 110px;
            height: 70px;
            border-radius: 4px;
          }
        }
        .title {
          width: 150px;
        }
        .ellipsis-text::after {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 20px; /* 渐变区域宽度 */
          height: 100%;
          background: linear-gradient(
            to right,
            rgba(255, 255, 255, 0),
            white
          ); /* 从透明到白色的渐变 */
          pointer-events: none; /* 确保不影响鼠标事件 */
        }
      }
    }
  }
}

.question-bottom {
  display: flex;
  flex-direction: row;
  margin-top: 53px;
  margin-bottom: 53px;
}

.lc-empty {
  height: 670px;
}
.no-more {
  width: 100%;
  display: flex;
  text-align: center;
  justify-content: center;
  color: var(--color-theme-project);
  font-family: var(--text-family);
  font-weight: 400;
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  margin: 10px 0;
}
.down-more {
  width: 100%;
  display: flex;
  justify-content: center;
  color: var(--color-theme-project);
  cursor: pointer;
}

.down-more:hover {
  font-weight: bolder;
}

/* 引号内容包装器 */
.quote-content-wrapper {
  display: flex;
  width: 100%;
  position: relative;
  white-space: nowrap;
  align-items: center; /* 确保所有子元素垂直居中 */
  height: 50px;
  overflow: hidden;
}

/* 左引号样式 */
.quote-left {
  flex-shrink: 0;
  margin-right: 1px; /* 增加右侧间距 */
  font-size: 16px; /* 调整引号大小 */
  line-height: 1;
  display: flex;
  align-items: center;
}

/* 右引号样式 */
.quote-right {
  flex-shrink: 0;
  margin-left: 4px; /* 增加左侧间距 */
  position: relative;
  z-index: 2; /* 确保引号在渐变层上方 */
  font-size: 16px; /* 调整引号大小 */
  line-height: 1;
  display: flex;
  align-items: center;
}

/* 省略号文本样式 */
.ellipsis-text {
  display: flex;
  max-width: calc(100% - 20px); /* 为引号预留更多空间 */
  color: black; /* 确保文字为黑色 */
  position: relative; /* 为渐变遮罩定位 */
  align-items: center; /* 垂直居中 */
  overflow: hidden;
  height: 100%;
}

/* 对不包含公式的内容应用省略号效果以及公式的垂直居中 */
:deep(.ellipsis-text p) {
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-height: 100%;
}
</style>
