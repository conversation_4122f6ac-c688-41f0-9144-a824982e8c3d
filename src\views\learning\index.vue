<template>
  <div class="LearnContainer">
    <div class="catalog">
      <PrjInfo v-if="ready" :buy-status="buyStatus" @chapter-change="handleChapterChange" />
    </div>
    <div class="main-content">
      <template v-if="ready">
        <template v-if="!NoPermisson">
          <PrjManuWrapper
            v-if="prjForm == PrjForm.draft"
            ref="pay"
            :payDialogVisible="payDialogVisible"
            :buyStatus="buyStatus"
            @update:payDialogVisible1="handleUpdatePayDialogVisible"
          />
          <PrjVideoWrapper v-else ref="pay" :payDialogVisible="payDialogVisible" :buyStatus="buyStatus" @update:payDialogVisible="handleUpdatePayDialogVisible" />
        </template>
        <OutPermission v-else :errorMessage="errorInfo"></OutPermission>
      </template>
    </div>

    <!-- 目前只有会员商品使用这个组件  -->
    <PayDialog
      v-model="payDialogVisible"
      v-if="ready && isVip"
      :spuId="info.spuId"
      :selectedSkuId="selectedSkuId"
      :isUserAdd="false"
      @paySuccess="handleBuy"
    />

    <!-- 从介绍页直接打开购买页的一定不是会员商品 会员商品需要先选择 -->
    <QRcodeDialog
      v-model="payDialogVisible"
      :skuId="skuId"
      v-if="ready && !isVip"
      @paySuccess="handleBuy"
    />
  </div>
</template>

<script setup lang="ts">
import { PrjForm, PrjType } from '@/types/project';
import PrjInfo from './components/PrjInfo.vue';
import type { PrjinfoItf } from '@/types/learning';
import { BuyStatus, GoodsType, PermissionCode } from '@/types/goods';
import { getPartProApi, getPrjDetailApi } from '@/apis/learning';
import PrjManuWrapper from './components/PrjManuWrapper.vue';
import PrjVideoWrapper from './components/PrjVideoWrapper.vue';
import { useLearningStore } from '@/stores/learning';
import { useDraftWordStoreV2 } from '@/stores/draftWordV2';
import { useVideoWordStoreV2 } from '@/stores/videoWordV2';
import OutPermission from '@/components/OutPermission.vue';
import { useProjectStore } from '@/stores/project';
import { getPrjIntroduceApi, getVipIntroduceApi } from '@/apis/case';

const learningStore = useLearningStore();
const draftWordStore = useDraftWordStoreV2();
const videoWordStore = useVideoWordStoreV2();

const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);

const isVip = ref(false); //是否是会员商品
const skuId = ref('');
const selectedSkuId = ref('');
const buyStatus = ref(false); // 是否已购买
const pay = ref(null);
const payDialogVisible = ref(false);

const ready = ref(false);
const route = useRoute();
const router = useRouter();
const spuId = route.query.spuId as string;
const chapterId = route.query.chapterId as string;
const NoPermisson = ref(false);
const errorInfo = ref('');

const handleBuy = async () => {
  // 获取最新的项目信息
  const res = await getPrjIntroduceApi({
    spuId: route.query.spuId as string
  });

  // 更新项目store
  projectStore.setPrjInfo(res.data);
  projectStore.setSpuId(spuId);

  // 更新本地状态 - 这里是关键，只更新权限相关状态
  if (res.data.buyStatus == BuyStatus.bought) {
    buyStatus.value = true;
  } else {
    buyStatus.value = false;
  }

  // 更新provide的prjInfo，让PrjInfo组件能获取到最新信息
  prjInfo.value = { ...prjInfo.value, ...res.data };

  // 显示购买成功提示
  ElMessage.success('购买成功！您现在可以访问所有章节内容了');
};

// 处理章节切换
const handleChapterChange = (chapterId: number, preview: boolean) => {
  // 如果是预览章节或者已购买，允许切换
  if (preview || buyStatus.value) {
    // 只更新路由参数，让子组件通过路由变化监听器处理数据更新
    router.replace({
      query: {
        ...route.query,
        chapterId: chapterId
      }
    });
  } else {
    // 没有权限，显示购买对话框
    payDialogVisible.value = true;
  }
};

// 处理PrjVideoWrapper的PayDialog显示事件
const handleUpdatePayDialogVisible = (value: boolean) => {
  payDialogVisible.value = value;
};

const prjInfo = ref<PrjinfoItf>({});
const prjForm = ref<PrjForm>();
const prjType = ref<PrjType>();

provide('prjInfo', prjInfo);
provide('prjForm', prjForm);
provide('prjType', prjType);
provide('buyStatus', buyStatus);
const useWideScreen = inject('useWideScreen') as Ref<boolean>;
onMounted(async () => {
  const res = await getPartProApi(spuId);
  prjInfo.value = res.data.list[0];
  prjForm.value = prjInfo.value.prjForm;
  prjType.value = prjInfo.value.prjType;
  if (prjForm.value == PrjForm.draft) {
    draftWordStore.$reset();
  } else {
    videoWordStore.$reset();
  }

  let res2 = await getPrjDetailApi(spuId, chapterId);
  if (String(res2.code) === PermissionCode.nobuy) {
    NoPermisson.value = true;
    errorInfo.value = res2.message || '获取项目详情失败';
    ready.value = true;
    return; // 如果没有权限，直接返回，不执行后续代码
  }
  // 只有在有权限的情况下才执行
  if (res2.data) {
    learningStore.setInfo(res2.data);
    router.replace({
      query: {
        ...route.query,
        chapterId: res2.data.chapterList[res2.data.validIndex].chapterId
      }
    });
  }
  useWideScreen.value = true;

  let getPrjIntroduce =
    typeof spuId === 'string' && spuId.startsWith('V') ? getVipIntroduceApi : getPrjIntroduceApi;
  const res3 = await getPrjIntroduce({ spuId: spuId });
  if (!res3.data) {
    ElMessage.error('项目不存在');
    router.push({ path: '/home' });
  } else {
    // 更新 projectStore 和 prjInfo
    projectStore.setPrjInfo(res3.data);
    projectStore.setSpuId(spuId);

    // 重要：更新 provide 的 prjInfo，让 PrjInfo 组件能获取到完整的项目信息
    prjInfo.value = { ...prjInfo.value, ...res3.data };

    skuId.value = info.value.priceList[0].skuId;
    if (info.value.goodsType == GoodsType.vip) {
      isVip.value = true;
    } else {
      isVip.value = false;
    }

    ready.value = true;
    if (info.value.buyStatus == BuyStatus.bought) {
      buyStatus.value = true;
    } else {
      buyStatus.value = false;
    }

    if (!buyStatus.value && isVip.value) {
      selectedSkuId.value = info.value.priceList[0].skuId;
    }
  }
});

watch(
  () => pay.value?.payDialogVisible,
  () => {
    payDialogVisible.value = pay.value?.payDialogVisible;
  }
);

onBeforeUnmount(() => {
  useWideScreen.value = false;
});
// // waqtodo 此处还需要传递 learnType
// const expandMapFn = () => {
//   // TODO:
//   // isMap.value = true;
// };
// const isTagExpand = ref(false);
// const isInfoExpand = ref(false);
// const curHeight = ref(70);
// const handleTagExpand = (expandHeight: string) => {
//   let newHeight = parseInt(expandHeight.replace('px', '')) + 38;
//   isTagExpand.value = !isTagExpand.value;
//   if (isTagExpand.value) {
//     if (isInfoExpand && curHeight.value >= newHeight) {
//       return;
//     }
//     (document.getElementById('infoBack') as HTMLElement).style.height = newHeight + 'px';
//     curHeight.value = newHeight;
//   } else {
//     (document.getElementById('infoBack') as HTMLElement).style.height = '70px';
//     (document.getElementById('infoBack') as HTMLElement).style.backgroundColor = 'white';
//     isInfoExpand.value = false;
//     curHeight.value = 70;
//   }
// };
// const handleInfoExpand = (expandHeight: string) => {
//   let newHeight = parseInt(expandHeight.replace('px', '')) + 38;
//   isInfoExpand.value = !isInfoExpand.value;
//   if (isInfoExpand.value) {
//     if (isTagExpand && curHeight.value >= newHeight) return;
//     (document.getElementById('infoBack') as HTMLElement).style.height = newHeight + 'px';
//     curHeight.value = newHeight;
//   } else {
//     (document.getElementById('infoBack') as HTMLElement).style.height = '70px';
//     (document.getElementById('infoBack') as HTMLElement).style.backgroundColor = 'white';
//     isTagExpand.value = false;
//     curHeight.value = 70;
//   }
// };
</script>

<style scoped lang="less">
.LearnContainer {
  display: flex;
  width: 100%;
  height: calc(100vh - 70px); /* 减去Header的70px高度 */
  overflow: hidden; /* 禁止页面级滚动 */
}

// 统一滚动条样式混合

// 左侧栏样式 - catalog
.catalog {
  width: 345px;
  background-color: #ffffff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
}

// 中间主内容区样式 - main-content
.main-content {
  flex: 1;
  overflow: hidden; /* 移除滚动条，让内部组件处理滚动 */
  padding: 0;
  display: flex;
  flex-direction: column;
}
</style>
