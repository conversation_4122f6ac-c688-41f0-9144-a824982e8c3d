<template>
  <div class="collect">
    <div class="collectup">
      <div class="backbuttom" @click="back">返回</div>
      <div class="collecttitle">收藏夹</div>
    </div>
    <div><el-divider></el-divider></div>

    <div class="collectdown" @click="handleWord" @mouseover="handleHover" @mouseout="cancelHover">
      <!-- <div class="projectname" v-html="collectdetail.stem"></div> -->
      <div class="left_operate">
        <div class="title">
          {{ questionTypeText }}
        </div>
        <div class="button-wrapper">
          <span class="hover_style" @click="removewindow()" style="margin-right: 15px"
            >移出收藏夹</span
          >
          <span class="hover_style" v-if="hasPrevious" @click="last()">上一个</span>
          <span class="hover_style" v-if="hasNext" @click="next()">下一个</span>
        </div>
      </div>
      <div><el-divider></el-divider></div>
      <div class="mode">
        <el-switch v-model="mode" />
        <span style="font-size: 12px; margin-left: 12px">{{ mode ? '提问模式' : '阅读模式' }}</span>
      </div>
      <div :class="['collectdown_detail', 'lineWordContent']">
        <div class="problemtitle" v-html="collectdetail.stem"></div>
        <div v-if="collectdetail!.type == ExerciseType.single">
          <el-form-item label="">
            <el-radio-group class="selection-style">
              <el-radio
                :label="indexIntoAlpha(index)"
                v-for="(selection, index) in collectdetail.content"
                :key="index"
              >
                <span class="inline-label">
                  <span>{{ indexIntoAlpha(index) }}</span
                  ><span v-html="selection.text"></span> </span
              ></el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div v-if="collectdetail!.type == ExerciseType.multi">
          <el-form-item label="">
            <el-checkbox-group class="selection-style">
              <el-checkbox
                :label="indexIntoAlpha(index)"
                v-for="(selection, index) in collectdetail.content"
                :key="index"
              >
                <span class="inline-label">
                  <span style="margin-right: 5px">{{ indexIntoAlpha(index) }}</span
                  ><span v-html="selection.text"></span>
                </span>
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        <!-- <div class="problem" id="htmlContent">
          <CollectCard :questionObj="collectdetail"></CollectCard>
        </div> -->
        <div class="answer-wrap">
          <div class="answer">
            <div class="detail">
              <div :class="['choice', 'lineWordContent']">
                <div v-if="collectdetail.type == ExerciseType.judge" class="unlineWordContent">
                  答案：{{
                    collectdetail.answer.replace(/<\/?span[^>]*>/g, '') == '1' ? '正确' : '错误'
                  }}
                </div>

                <div
                  v-else-if="
                    collectdetail.type == ExerciseType.multi ||
                    collectdetail.type == ExerciseType.single
                  "
                >
                  答案：<span
                    v-html="
                      JSON.parse(collectdetail.answer.replace(/<\/?span[^>]*>/g, '')).join(', ')
                    "
                  ></span>
                </div>
                <div v-else>答案：<span v-html="collectdetail.answer"></span></div>
              </div>
              <div :class="['description', 'lineWordContent']">
                说明：
                <div class="lineWordContent" v-html="collectdetail.explanation"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="detail">
import CollectCard from '@/components/CollectCard.vue';
import type { documentData, collectData } from '@/types/data';
import {
  getCollectDetailDataApi,
  removeCollectDetailDataApi,
  cancelFavoritesApi
} from '@/apis/collect';
import { getExerciseApi } from '@/apis/collect';
import { useLearningStore } from '@/stores/learning';
import type { exerciseItem } from '@/types/exercise';
import { useIdListStore } from '@/stores/idListStore';
import { ExerciseType, QuestionListData } from '@/types/exercise';
import { useExerciseStore } from '@/stores/exercise';
import indexIntoAlpha from '@/utils/indexIntoAlpha';

import { getQuestionListApi } from '@/apis/exercise';

const idListStore = useIdListStore();

const learningStore = useLearningStore();
const exerStore = useExerciseStore();
const drawerControllerStore = useDrawerControllerStore();
const router = useRouter();
const route = useRoute();
const { mode } = storeToRefs(drawerControllerStore);
//这里后续可能需要传递参数！！！回退都上一页
const back = () => {
  router.push('/mystudy/collect');
};

const collectdetail = ref<exerciseItem>({
  exerciseId: '',
  type: 0, //1.单选 2多选 3填空 4判断
  stem: '', //题目
  content: [],
  answer: '', //答案
  explanation: '' //解释说明
});

const questionTypeText = computed(() => {
  switch (collectdetail.value.type) {
    case 1:
      return '单选题';
    case 2:
      return '多选题';
    case 3:
      return '填空题';
    case 4:
      return '判断题';
    default:
      return '全部题型';
  }
});
const idList = ref<string[]>([]);
const exerciseId = ref('');
const questionList = shallowRef<QuestionListData[]>([]);
const currentIndex = computed(() => idList.value.indexOf(exerciseId.value));
const hasPrevious = computed(() => currentIndex.value > 0);
const hasNext = computed(() => currentIndex.value < idList.value.length - 1);

//划词
import { useWordStore } from '@/stores/word';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { intersection } from 'lodash-es';
import { emitter } from '@/utils/emitter';
import { useExerciseWordStoreV2 } from '@/stores/exerciseWordV2';
import { Mode, QuestionAction, type RenderInfo } from '@/types/word';
import { handleVideoQuestion } from '@/utils/handleQuestion';
import { Event } from '@/types/event';

const exerciseWordStore = useExerciseWordStoreV2();
const matchingColor = computed(() => {
  return mode.value == Mode.ask ? '' : '#FFDD6C';
});
const matchingHeight = computed(() => {
  return mode.value == Mode.ask ? '' : '700';
});

const wordStore = useWordStore();
const saveType = inject('saveType') as Ref;

const handleQuestionList = async () => {
  //（获取问题列表）
  const res = await getQuestionListApi(exerciseId.value);
  questionList.value = res.data.list;
  console.log('questionList:', questionList.value);

  const worker = new Worker(new URL('@/utils/exerciseWorker.ts', import.meta.url), {
    type: 'module'
  });
  worker.onmessage = function (
    e: MessageEvent<{
      exerciseStringList: Array<{ list: Array<string>; start: number }>;
      originalExerciseStringList: Array<{ list: Array<string>; start: number }>;
      regString: string;
      unitList: Array<{
        tagName: string;
        children: any[];
        index: number;
        qids: number[];
        highlight: boolean;
        stringIndex: number;
      }>;
      uncommonWordMap: Map<string, string>;
    }>
  ) {
    wordStore.exerciseStringList = e.data.exerciseStringList;
    wordStore.originalExerciseStringList = e.data.originalExerciseStringList;
    wordStore.regString = e.data.regString;
    wordStore.unitList = e.data.unitList;
    wordStore.uncommonWordMap = e.data.uncommonWordMap;

    let index = 0;
    let tempList = [];
    for (let i = 0; i < wordStore.exerciseStringList.length; i++) {
      // 获取当前项的 HTML 元素字符串
      const item = wordStore.getExerciseStringList[index++];
      tempList.push(item);
    }
    //console.log('tempList:', tempList);
    exerStore.setExercise(transferList2Exercise(tempList));

    collectdetail.value = exerStore.exercise;
    collectdetail.value.content = exerStore.exercise.option;

    worker.terminate();
  };
  worker.postMessage({
    questionList: toRaw(questionList.value),
    exercise: toRaw(exerStore.exercise)
  });
};
// 把worker的list转化为exercise
const transferList2Exercise = (list: any[]): any => {
  const tempExercise = ref<{
    type: number;
    stem: string;
    option: any[];
    answer: string;
    explanation: string;
    exerciseId: string;
  }>({
    type: 0,
    stem: '',
    option: [],
    answer: '',
    explanation: '',
    exerciseId: ''
  });
  const tempList = ref<any[]>([]);
  list.forEach((item: any) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(item, 'text/html');
    const span = doc.querySelector('span'); // 获取最外层的 span 元素
    if (span && span.hasAttribute('etype')) {
      const etype = span.getAttribute('etype');
      if (span.hasAttribute('type')) {
        const type = span.getAttribute('type');
        if (type) {
          tempExercise.value.type = parseInt(type);
        }
      }
      if (etype === 'option') {
        const op = {
          optionId: span.getAttribute('optionid'),
          text: item
        };
        tempList.value.push(op);
      } else {
        tempExercise.value[etype] = item;
      }
    }
  });
  tempExercise.value.option = tempList.value;
  return tempExercise.value;
};
const transferList2Exercise2 = (list: any[]): any => {
  const tempExercise = ref<{
    type: number;
    stem: string;
    content: any[];
    answer: string;
    explanation: string;
  }>({
    type: 0,
    stem: '',
    content: [],
    answer: '',
    explanation: ''
  });
  const tempList = ref<any[]>([]);
  list.forEach((item: any) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(item, 'text/html');
    const span = doc.querySelector('span'); // 获取最外层的 span 元素
    if (span && span.hasAttribute('etype')) {
      const etype = span.getAttribute('etype');
      if (span.hasAttribute('type')) {
        const type = span.getAttribute('type');
        if (type) {
          tempExercise.value.type = parseInt(type);
        }
      }
      if (etype === 'content') {
        const op = {
          optionId: span.getAttribute('contentid'),
          text: item
        };
        tempList.value.push(op);
      } else {
        tempExercise.value[etype] = item;
      }
    }
  });
  tempExercise.value.content = tempList.value;
  return tempExercise.value;
};
watch(
  () => mode,
  (newValue, oldValue) => {
    if (newValue.value) {
      saveType.value = 1;
      if (newValue != oldValue)
        collectdetail.value = transferList2Exercise2(exerciseWordStore.getAskModeExerciseStrings());
    } else {
      if (newValue != oldValue)
        collectdetail.value = transferList2Exercise2(
          exerciseWordStore.getReadModeExerciseStrings()
        );
    }
  },
  { deep: true, immediate: true }
);
const handleQuestionList2 = async () => {
  // 处理习题
  const handleExercise = (exercise: any): string[] => {
    const tempExerciseList = [];
    tempExerciseList.push(
      `<span etype="stem" type="${exercise.type}" style="width: 100%;">${exercise.stem}</span>`
    );
    if (exercise.content) {
      exercise.content.forEach((item) => {
        tempExerciseList.push(
          `<span etype="content" contentid="${item.optionId}">${item.text}</span>`
        );
      });
    }
    tempExerciseList.push(`<span etype="answer">${exercise.answer}</span>`);
    tempExerciseList.push(`<span etype="explanation">${exercise.explanation}</span>`);
    return tempExerciseList;
  };

  const res = await getQuestionListApi(exerciseId.value);
  questionList.value = res.data.list;
  const worker = new Worker(new URL('@/worker/exerciseWorker.ts', import.meta.url), {
    type: 'module'
  });
  console.log('worker-curren', collectdetail.value);
  const htmlStrings = handleExercise(collectdetail.value);
  console.log('html:', htmlStrings);
  worker.onmessage = function (
    e: MessageEvent<{
      regString: string;
      renderInfoIndexes: Array<{
        listIndex: number;
        index: number;
      }>;
      renderInfoListList: RenderInfo[][];
    }>
  ) {
    exerciseWordStore.regString = e.data.regString;
    console.log('exerciseWordStore', exerciseWordStore.regString);
    exerciseWordStore.renderInfoIndexes = e.data.renderInfoIndexes;
    exerciseWordStore.renderInfoListList = e.data.renderInfoListList;
    toRaw(questionList.value)?.forEach((question) => {
      handleVideoQuestion(
        question,
        toRaw(exerciseWordStore.regString),
        toRaw(exerciseWordStore.renderInfoIndexes),
        toRaw(exerciseWordStore.renderInfoListList)
      );
    });
    collectdetail.value = transferList2Exercise2(exerciseWordStore.getReadModeExerciseStrings());
  };
  worker.postMessage({
    htmlStrings,
    regString: toRaw(exerciseWordStore.regString),
    renderInfoIndexes: toRaw(exerciseWordStore.renderInfoIndexes),
    renderInfoListList: toRaw(exerciseWordStore.renderInfoListList)
  });
};
const handleWord = inject('handleWord') as (e: Event) => void;
const handleHover = (e: Event) => {
  let element = e.target as HTMLElement;
  if ((e.target as HTMLElement).closest("[class^='inline-equation'], [class^='equation']")) {
    element = (e.target as HTMLElement).closest(
      "[class^='inline-equation'], [class^='equation']"
    ) as HTMLElement;
  }
  element = element.closest('[data-qid]') as HTMLElement;
  if (element && element.classList.contains('highlight')) {
    element.classList.add('highlightHover');
    const index = parseInt(element.getAttribute('data-index') as string);
    const rootQids = (element.getAttribute('data-qid') as string).split(',');
    for (let i = index - 1; ; i--) {
      const ele = document.querySelector(`span[data-index="${i}"]`) as HTMLElement;
      if (!ele) {
        break;
      }
      const qidString = ele.getAttribute('data-qid');
      if (qidString) {
        const qids = qidString.split(',');
        if (intersection(rootQids, qids).length > 0) {
          ele.classList.add('highlightHover');
        } else {
          break;
        }
      } else {
        break;
      }
    }
    for (let i = index + 1; ; ++i) {
      const ele = document.querySelector(`span[data-index="${i}"]`) as HTMLElement;
      if (!ele) {
        break;
      }
      const qidString = ele.getAttribute('data-qid');
      if (qidString) {
        const qids = qidString.split(',');
        if (intersection(rootQids, qids).length > 0) {
          ele.classList.add('highlightHover');
        } else {
          break;
        }
      } else {
        break;
      }
    }
  }
};
const cancelHover = (e: Event) => {
  const elements = document.querySelectorAll('.highlightHover');
  elements.forEach((element) => {
    element.classList.remove('highlightHover');
  });
};

watch(
  () => route.query.exerciseId,
  async (newVal) => {
    if (newVal) {
      exerciseId.value = newVal as string;
      const res = await getExerciseApi(exerciseId.value);
      collectdetail.value = res.data;

      exerStore.setExerciseId(exerciseId.value);

      exerciseWordStore.regString = '';
      handleQuestionList2();

      emitter.emit('initHandler', () => {
        exerciseWordStore.regString = '';
        handleQuestionList2();
      });
    }
  }
);
//获取题目的详细数据
const getCollectData = async (exerciseId: string) => {
  const res = await getExerciseApi(exerciseId);
  collectdetail.value = res.data;
};

onMounted(async () => {
  exerciseId.value = route.query.exerciseId as string;
  await getCollectData(exerciseId.value);
  //exerStore.setExercise(collectdetail.value);
  exerStore.setExerciseId(exerciseId.value);
  exerciseWordStore.regString = '';
  handleQuestionList2();

  idList.value = idListStore.getIdList;
  emitter.emit('initHandler', () => {
    exerciseWordStore.regString = '';
    handleQuestionList2();
  });
});

const next = async () => {
  if (hasNext.value) {
    const nextId = idList.value[currentIndex.value + 1];
    await getCollectData(nextId);
    router.push({
      path: route.path,
      query: {
        ...route.query,
        exerciseId: nextId
      }
    });
  }
};
const last = async () => {
  if (hasPrevious.value) {
    const previousId = idList.value[currentIndex.value - 1];
    await getCollectData(previousId);
    router.push({
      path: route.path,
      query: {
        ...route.query,
        exerciseId: previousId
      }
    });
  }
};
//移除题目
//移除以后需要进行判断，如果有下一个就展示下一个页面，如果没有下一个就展示上一个页面，同时也要切换localstorage
const remove = async () => {
  await cancelFavoritesApi(exerciseId.value);
  next();
  last();
};
//弹窗
const removewindow = () => {
  ElMessageBox.confirm('是否将当前题目移出收藏夹', '移出收藏夹', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      await remove();
      ElMessage({
        type: 'success',
        message: '已成功移出收藏夹'
      });
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消当前操作'
      });
    });
};
</script>

<style lang="less" scoped>
.collect {
  width: 1100px;
  // height: 600px;
  margin-left: 10px;
  padding-left: 10px;
  padding-right: 18px;
  display: flex;
  flex-direction: column;

  .collectup {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-top: 8px;

    .backbuttom {
      width: 80px;
      height: 59px;
      background-color: var(--color-theme-project);
      border-radius: 4px;
      font-size: 14px;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.996);
      line-height: normal;
      font-feature-settings: 'kern';
      border-width: 0.8px;
      border-style: solid;
      padding-top: 20px;
      padding-left: 26px;
      // font-size: var(--fontsize-large-project);
      cursor: pointer;
    }

    .backbuttom:hover {
      border-color: var(--color-theme-project);
      background-color: var(--color-second);
      color: var(--color-theme-project);
    }

    .collecttitle {
      font-size: 18px;
      font-weight: 700;
      color: #333333;
      line-height: normal;
      font-feature-settings: 'kern';
      margin-left: 16px;
    }
  }

  .collectdown {
    margin-left: 50px;

    .left_operate {
      display: flex;
      //flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .title {
        width: 100%;
        height: 33px;
        line-height: 33px;
        font-size: 16px;
        font-weight: 700;
        //background-color: #fffff;
        padding-left: 10px;
        //border-bottom: 2px solid #f2f2f2;
      }
      .button-wrapper {
        //margin-left: auto;
        display: flex;
        .hover_style {
          //margin-left: 20px;
          font-size: 14px;
          font-weight: 400;
          color: #333333;
          line-height: normal;
          font-feature-settings: 'kern';
          width: 80px;
        }

        .hover_style:hover {
          color: #005579;
        }
      }
    }

    .collectdown_detail {
      margin-top: 8px;
      display: flex;
      flex-direction: column;

      .selection-style {
        display: flex;
        flex-direction: column;
        margin-top: 15px;
        align-items: flex-start;
        max-height: 220px; /* 控制最大高度，设置合适的值 */
        overflow-y: auto; /* 添加竖向滚动条 */
        overflow-x: hidden;
        .inline-label {
          display: inline-flex;
          align-items: center;
        }

        .el-radio {
          width: 100%;
          margin-bottom: 5px;
          white-space: nowrap; /* 保证文字不换行 */
          :is(span) {
            // margin-left: 32px;
            margin-right: 5px;
            :deep(.el-radio__input) {
              display: none !important;
            }
          }
        }
        .el-checkbox {
          width: 100%;
          margin-bottom: 5px;
          white-space: nowrap; /* 保证文字不换行 */

          :deep(.el-checkbox__input) {
            display: none !important;
          }
        }
      }
      .answer-wrap {
        width: 100%;
        display: flex;
        //justify-content: center;
        margin-top: 10px;
        max-height: 235px;
        overflow-y: auto;

        .answer {
          width: 92%;

          .msg {
            height: 37px;
            display: flex;
            align-items: center;

            width: 100%;

            img {
              margin-left: 7px;
              margin-right: 7px;
            }
          }

          .correct-color {
            color: #67c23a;
            background-color: #f0f9eb;
          }

          .error-color {
            background-color: #fdf6ec;
            color: #e6a23c;
          }

          .detail {
            width: 100%;
            // border: 1px solid #f2f2f2;
            // border-radius: 4px;
            margin-top: 4px;
            //padding-left: 25px;
            padding-right: 16px;
            font-size: 14px;

            .choice {
              display: flex;
              flex-direction: row;
              width: 100%;
              justify-content: space-between;
            }

            .description {
              margin-top: 20px;
              white-space: pre-wrap;
            }
          }
        }
      }
      .problemtitle {
        //height: 33px;
        padding-top: 9px;
        //padding-left: 2px;
        // background-color: #f2f2f2;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: normal;
        font-feature-settings: 'kern';
      }

      .problem {
        display: flex;
        flex-direction: row;
        margin-top: 15px;
        justify-content: space-between;
      }
    }
    &:deep(.search-keys) {
      color: red;
      cursor: pointer;
      font-weight: 700;
    }
    &:deep(.highlight2) {
      // color: var(--color-theme-project);
      background-color: v-bind(matchingColor);
      cursor: pointer;
      font-weight: v-bind(matchingHeight);
    }
    &:deep(.activeElement) {
      background-color: #ff9632;
    }
  }
  // :deep(.highlight) {
  //   color: var(--color-theme-project);
  //   cursor: pointer;
  // }
  :deep(.highlightHover) {
    font-weight: 700;
    * {
      font-weight: 700;
    }
  }
}
</style>
